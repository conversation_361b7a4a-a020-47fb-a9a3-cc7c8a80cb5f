const express = require("express");
const app = express();
const cors = require("cors");
var jwt = require("jsonwebtoken");
const jsSHA = require("jssha");
const request = require("request");
// const sha = new jsSHA("SHA-512", "TEXT");
const bodyParser = require("body-parser");
require("dotenv").config();
const nodemailer = require("nodemailer");
const schedule = require("node-schedule");
var pdf = require("pdf-creator-node");
const fs = require("fs");
const path = require("path");
var parser = require("xml2json");
const admzip = require("adm-zip");
const multer = require("multer");
var mammoth = require("mammoth");
const axios = require("axios");
const { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } = require("@aws-sdk/client-s3");
const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
const api = "https://phpstack-702151-2512759.cloudwaysapps.com";
const emails = "<EMAIL>,<EMAIL>";
const Insta = require("instamojo-nodejs");
const htmlToDocx = require("html-docx-js");
var whitelist = [
  "http://localhost:3000",
  "http://ncexams.net",
  "https://exams.navachaitanya.net",
];
const http = require("http");
const crypto = require("crypto");
const server = http.createServer(app);
const { google } = require("googleapis");

const socketIo = require("socket.io");

// Google Drive API helper functions using direct REST API calls
const getDriveAccessToken = async () => {
  try {
    // Read the service account key file
    console.log("Reading service account file: drive_json.json");
    const serviceAccount = JSON.parse(fs.readFileSync("drive_json.json", "utf8"));

    console.log("Service account email:", serviceAccount.client_email);
    console.log("Service account project ID:", serviceAccount.project_id);

    // Create JWT payload
    const now = Math.floor(Date.now() / 1000);
    const payload = {
      iss: serviceAccount.client_email,
      scope: "https://www.googleapis.com/auth/drive.file",
      aud: "https://oauth2.googleapis.com/token",
      exp: now + 3600,
      iat: now,
    };

    console.log("JWT payload:", payload);

    // Sign JWT with private key
    const token = jwt.sign(payload, serviceAccount.private_key, { algorithm: "RS256" });

    // Exchange JWT for access token
    console.log("Requesting access token from Google...");
    const response = await axios.post("https://oauth2.googleapis.com/token", {
      grant_type: "urn:ietf:params:oauth:grant-type:jwt-bearer",
      assertion: token,
    });

    console.log("Access token obtained successfully");
    return response.data.access_token;
  } catch (error) {
    console.error("Error getting Drive access token:");
    console.error("Error message:", error.message);
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response data:", error.response.data);
    }
    throw error;
  }
};

const uploadFileToDrive = async (accessToken, fileName, pdfBuffer) => {
  try {
    console.log(`Starting file upload: ${fileName}, size: ${pdfBuffer.length} bytes`);

    const boundary = "-------314159265358979323846";
    const delimiter = `\r\n--${boundary}\r\n`;
    const close_delim = `\r\n--${boundary}--`;

    // File metadata
    const metadata = {
      name: fileName,
      parents: [], // Root folder
    };

    console.log("File metadata:", metadata);

    // Create multipart body
    let multipartRequestBody =
      delimiter +
      "Content-Type: application/json\r\n\r\n" +
      JSON.stringify(metadata) +
      delimiter +
      "Content-Type: application/pdf\r\n" +
      "Content-Transfer-Encoding: base64\r\n\r\n" +
      pdfBuffer.toString("base64") +
      close_delim;

    console.log("Making upload request to Google Drive API...");
    const response = await axios.post(
      "https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&fields=id,name,webViewLink,webContentLink",
      multipartRequestBody,
      {
        headers: {
          "Content-Type": `multipart/related; boundary="${boundary}"`,
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    console.log("Upload successful, file ID:", response.data.id);
    return response.data;
  } catch (error) {
    console.error("Error uploading file to Drive:");
    console.error("Error message:", error.message);
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response statusText:", error.response.statusText);
      console.error("Response data:", error.response.data);
      console.error("Response headers:", error.response.headers);
    }
    throw error;
  }
};

const setDriveFilePermissions = async (accessToken, fileId) => {
  try {
    const response = await axios.post(
      `https://www.googleapis.com/drive/v3/files/${fileId}/permissions`,
      {
        role: "reader",
        type: "anyone",
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error("Error setting Drive file permissions:", error);
    throw error;
  }
};

const deleteFileFromDrive = async (accessToken, fileId) => {
  try {
    await axios.delete(`https://www.googleapis.com/drive/v3/files/${fileId}`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
  } catch (error) {
    if (error.response && error.response.status === 404) {
      // File already deleted, that's fine
      return;
    }
    console.error("Error deleting file from Drive:", error);
    throw error;
  }
};

var corsOptions = {
  origin: function (origin, callback) {
    if (whitelist.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error("Not allowed by CORS"));
    }
  },
};
const sms = require("./sms.js");

app.use(cors());
app.use(express.json({ limit: '100mb' })); // Increase JSON payload limit for PDF uploads
app.use(express.urlencoded({ limit: '100mb', extended: true })); // Increase URL encoded limit
app.use(
  bodyParser.urlencoded({
    extended: true,
    limit: "100mb", // Increase limit for PDF uploads
    parameterLimit: 500000,
  })
);
app.use(bodyParser.json({ limit: '100mb' })); // Add JSON body parser with increased limit

// Serve static files for uploaded PDFs
app.use('/uploads', express.static(path.join(__dirname, '../public/uploads')));
var mysql = require("mysql");
const OpenAI = require("openai");
// var connection
// = mysql.createConnection({
//     host: 'localhost',
//     user: 'nnekynbxen',
//     password: 'UsDrU4QFh4',
//     database: 'nnekynbxen'
// })

var pool = mysql.createPool({
  host: "**************",
  user: "nnekynbxen",
  password: "UsDrU4QFh4",
  database: "nnekynbxen",
  //  waitForConnections: true,
  connectionLimit: 10,
  // idleTimeout: 25200,
  allowPublicKeyRetrieval: true,
  // queueLimit: 0
});
async function runQuery(sqlQuery) {
  return new Promise(async (resolve, reject) => {
    if (pool) {
      try {
        await pool.getConnection(async function (err, connection2) {
          if (err) return reject(false); // not connected!
          await connection2.query(sqlQuery, function (error, results, fields) {
            connection2.destroy();
            console.log(pool._freeConnections.indexOf(connection2)); // 0
            if (error) return reject(false);
            return resolve(results);
          });
        });
      } catch (error) {
        return reject(false);
      }
    } else {
      return reject(false);
    }
  });
}
var connection = {
  query: async (e, cb) => {
    try {
      console.log(e, "Quer");
      const data = await runQuery(e);
      console.log("data", "data");
      if (data) {
        cb(null, data);
      } else {
        cb(true, null);
      }
    } catch (error) {}
  },
};

 

// Simple rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map();

// Rate limiting helper function
const checkRateLimit = (userPhone, endpoint, maxRequests = 10, windowMs = 15 * 60 * 1000) => {
  const key = `${userPhone}:${endpoint}`;
  const now = Date.now();
  const windowStart = now - windowMs;

  if (!rateLimitStore.has(key)) {
    rateLimitStore.set(key, []);
  }

  const requests = rateLimitStore.get(key);
  // Remove old requests outside the window
  const validRequests = requests.filter(timestamp => timestamp > windowStart);

  if (validRequests.length >= maxRequests) {
    return false; // Rate limit exceeded
  }

  validRequests.push(now);
  rateLimitStore.set(key, validRequests);
  return true; // Request allowed
};

// Access logging helper function
const logFileAccess = async (userPhone, quizId, fileName, action, success = true, error = null) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    user_phone: userPhone,
    quiz_id: quizId,
    file_name: fileName,
    action: action, // 'upload', 'access', 'download'
    success: success,
    error: error,
    ip_address: 'server-side' // You can pass req.ip if needed
  };

  console.log('File Access Log:', JSON.stringify(logEntry));

  // Optional: Store in database for audit trail
  // try {
  //   await runQuery(
  //     `INSERT INTO file_access_logs (user_phone, quiz_id, file_name, action, success, error_message, log_date)
  //      VALUES (?, ?, ?, ?, ?, ?, NOW())`,
  //     [userPhone, quizId, fileName, action, success, error]
  //   );
  // } catch (err) {
  //   console.error('Failed to log file access:', err);
  // }
};

var pool2 = mysql.createPool({
  host: "**************",
  user: "ecujpeqyts",
  password: "UsDrU4QFh4",
  database: "ecujpeqyts",
  //  port: 3306,
  //  waitForConnections: true,
  connectionLimit: 1,
  idleTimeout: 25200,
  allowPublicKeyRetrieval: true,
  // queueLimit: 0
});
async function runQuery2(sqlQuery) {
  return new Promise(async (resolve, reject) => {
    if (pool2) {
      await pool2.getConnection(async function (err, connection2) {
        if (err) throw err; // not connected!
        await connection2.query(sqlQuery, function (error, results, fields) {
          connection2.destroy();
          console.log(pool2._freeConnections.indexOf(connection2)); // 0
          if (error) return reject(false);
          return resolve(results);
        });
      });
    } else {
      return reject(false);
    }
  });
}
var Tsconnection = {
  query: async (e, cb) => {
    const data = await runQuery2(e);
    // console.log("data", "data")
    if (data) {
      cb(null, data);
    } else {
      cb(true, null);
    }
  },
};
// console.log(connection);
const io = socketIo(server, {
  cors: {
    origin: "*",
  },
});
let connectedUsers = {};
let messages = [];
const myNamespace = io.of("/my-app-chat");

myNamespace.on("connection", (socket) => {
  // console.log("User connected", socket.id);

  // Handle new user connection
  socket.on("newUser", async (name, phonenumber, oneSignalId) => {
    if (phonenumber) {
      socket.join(phonenumber);
      if (oneSignalId) {
        await connection.query(
          `CALL supportHome('storeUserId', '${oneSignalId}', '${phonenumber}')`,
          async function (err, resu) {
            // if (err) response.send(err);
            // response.send(resu);
            if (resu[0][0].firstTime === 1) {
              const msg = {
                type: "sendUserMsg",
                search: "Welcome to NC Exams",
                qid: `Admin_${phonenumber}_1_0`,
                stateType: "ap",
              };
              storeINExcel(name, phonenumber, "New User From APP", false);
              sendMessage(msg);
              axios.post(
                `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
                {
                  businessId: 1,
                  verifyToken: Math.random() * 15000,
                  phoneNumber: phonenumber,
                  message: [],
                  messageType: "promotion",
                  templateLang: "te",
                  templateName: "user_reg_sucess_message",
                }
              );
            }
          }
        );
        await Tsconnection.query(
          `CALL supportHome('storeUserId', '${oneSignalId}', '${phonenumber}')`,
          async function (err, resu) {
            // if (err) response.send(err);
            // response.send(resu);
          }
        );
      }

      // try {
      //   await connection.query(
      //     `CALL verifyAdmin('${phone}')`,
      //     async function (error, results) {
      //       if (error) res.send(401).send(error);
      //       if (results[0][0].result === "success") {
      //         connectedUsers['Admin'] = socket.id;
      //       } else {
      //         connectedUsers[phonenumber] = socket.id;
      //       }
      //     }
      //   );
      connectedUsers[phonenumber] = socket.id;
      // } catch (error) {
      //   // Access Denied

      // }
    }
  });

  // Send stored messages to newly connected client

  // Receive new messages
  socket.on("message", async (msg) => {
    console.log("New message:", msg);
    sendMessage(msg);
  });

  const sendMessage = async (msg) => {
    try {
      // messages.push(newMessage);
      const { type, search, qid, stateType } = msg;
      await connection.query(
        `CALL supportHome('${type}', '${search}', '${qid}')`,
        async function (err, resu) {}
      );
      let from = qid.split("_")[0];
      let to = qid.split("_")[1];
      let isGroup = qid.split("_")[3];
      const newMessage = {
        senttime: new Date().toLocaleTimeString(),
        id: Math.floor(Math.random() * 1000), // Just a temporary ID generation
        from,
        to,
        message: search,
        type: "recieve",
      };
      myNamespace.in(to).emit("message", newMessage, connectedUsers[from]);
      myNamespace.in(from).emit("online", connectedUsers[to]);

      const messgeType = to !== "Admin" ? "getOnsignalId" : "getIndivUserId";
      const isGr = to === "Admin" ? from : "1";

      var headers = {
        "Content-Type": "application/json; charset=utf-8",
        Authorization: `Basic Mzc5NjY0ZjgtNjUxMC00YjNmLTk1MWEtZGUxMmUwNDMwNTJj`,
      };
      if (to === "All") {
        await axios.post(
          `https://api.onesignal.com/notifications`,
          {
            app_id: "4f2fd23f-377c-40be-8cca-7867d0e6fdd6",
            name: "string",
            contents: {
              en: search,
            },
            headings: {
              en: "Admin sent you a message",
            },

            url: "https://exams.navachaitanya.net/support/user/All",
            // include_player_ids: each,
            included_segments: ["Subscribed Users"],
          },
          { headers }
        );
      } else {
        const connect = stateType === "ap" ? connection : Tsconnection;
        connect.query(
          `CALL supportHome('${messgeType}', '${to}','${isGr}')`,
          async function (err, resu) {
            if (resu.length > 0) {
              let allSgnalIds = {};
              let count = 1;
              let users = 1;
              let userName = "";
              for (let each of resu[0]) {
                if (each.onesignalId) {
                  if (!allSgnalIds[count]) {
                    allSgnalIds[count] = []; // Initialize as an array if not already
                  }
                  allSgnalIds[count].push(each.onesignalId);
                  userName = each.userName;
                  users++;
                }
                if (users === 1999) {
                  count++;
                }
              }

              const allUsers = Object.values(allSgnalIds);
              for (let each of allUsers) {
                await axios.post(
                  `https://api.onesignal.com/notifications`,
                  {
                    app_id: "4f2fd23f-377c-40be-8cca-7867d0e6fdd6",
                    name: "string",
                    contents: {
                      en: search,
                    },
                    headings: {
                      en:
                        to === "Admin"
                          ? userName + " sent you a message"
                          : "Admin sent you a message",
                    },
                    url: "https://exams.navachaitanya.net/support/user/" + from,
                    include_player_ids: each,
                  },
                  { headers }
                );
              }
            }
          }
        );
      }
    } catch (error) {}
  };

  socket.on("online", async (from, to) => {
    myNamespace.in(from).emit("online", connectedUsers[to]);
  });
  socket.on("getUserChat", async (selectedChat, userType) => {
    await connection.query(
      `CALL supportHome("getUserChat", '${selectedChat}', '${userType}')`,
      async function (err, resu) {
        if (resu) {
          socket.emit("getUserChat", resu);
        }
      }
    );
  });
  socket.on("supportHomeAll", async (uid) => {
    await connection.query(
      `CALL supportHome("supportHomeAll", '${uid}', '')`,
      async function (err, resu) {
        if (resu) {
          await Tsconnection.query(
            `CALL supportHome("supportHomeAll", '${uid}', '')`,
            async function (err, resu2) {
              if (resu2) {
                socket.emit("supportHomeAll", resu.concat(resu2));
              }
            }
          );
        }
      }
    );
  });

  // Handle disconnections
  socket.on("disconnect", () => {
    // console.log("User disconnected");
    // Remove disconnected user from connected users list
    for (let username in connectedUsers) {
      if (connectedUsers[username] === socket.id) {
        delete connectedUsers[username];
        break;
      }
    }
  });
});
// console.log(connection);
if (!fs.existsSync("public")) {
  fs.mkdirSync("public");
}
if (!fs.existsSync("public/uploads")) {
  fs.mkdirSync("public/uploads");
}
if (!fs.existsSync("reported")) {
  fs.mkdirSync("reported");
}
var storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, "public/uploads");
  },
  filename: function (req, file, cb) {
    cb(null, "Q.docx");
    console.log(file.originalname);
  },
});

const storeINExcel = async (studentName, phone, message, messaged = true) => {
  const auth = new google.auth.GoogleAuth({
    keyFile: "google.json",
    scopes: "https://www.googleapis.com/auth/spreadsheets",
  });

  // Create client instance for auth
  const client = await auth.getClient();

  // Instance of Google Sheets API
  const googleSheets = google.sheets({ version: "v4", auth: client });

  const spreadsheetId = "1M87kUT6TIh3bLkrFybhhJnhyRNyvFX3x_79GY6ed0Bo";

  // Write row(s) to spreadsheet
  await googleSheets.spreadsheets.values.append({
    auth,
    spreadsheetId,
    range: messaged ? "MessagedUsers!A:D" : "RegisteredUsers!A:D",
    valueInputOption: "USER_ENTERED",
    resource: {
      values: [[studentName, phone, message, new Date().toLocaleString()]],
    },
  });
};
const verifyJwt = (req, res, next) => {
  try {
    const token = req.headers["authorization"];
    // console.log(token);
    const verified = jwt.verify(token, "nookenavanishincexams");
    if (verified) {
      req.phone = verified.number;
      next();
    } else {
      // Access Denied
      return res.status(401).send(error);
    }
  } catch (error) {
    // Access Denied
    return res.status(401).send(error);
  }
};

const verifyAdmin = async (req, res, next) => {
  const { phone } = req;
  console.log(phone);
  try {
    // await connection.query(`commit;`, async function (error, results) {});
    const admins = {
      **********: true,
      **********: true,
    };
    if (admins[phone]) {
      next();
    } else {
      return res.status(403).send("Access Denied");
    }
  } catch (error) {
    console.log("🚀 ~ verifyAdmin ~ error:", error);
    // Access Denied
    next();
    // return res.status(401).send(error);
  }
};

async function mailer(htmlData, tit) {
  // Generate test SMTP service account from ethereal.email
  // Only needed if you don't have a real mail account for testing
  // console.log(process.env.GMAIL, process.env.GMAIL_PASS);
  // create reusable transporter object using the default SMTP transport
  let transporter = nodemailer.createTransport({
    host: "smtp.gmail.com",
    port: 465,
    secure: true,
    auth: {
      user: "<EMAIL>",
      pass: "bqzfomfljafnkqta", // generated ethereal password
      // },
    },
  });

  transporter.set("oauth2_provision_cb", (user, renew, callback) => {
    let accessToken = userTokens[user];
    if (!accessToken) {
      return callback(new Error("Unknown user"));
    } else {
      return callback(null, accessToken);
    }
  });
  // send mail with defined transport object
  let info = await transporter.sendMail({
    from: "NavaCHAITANYA <NAME_EMAIL>", // sender address
    to: emails, // list of receivers
    subject: tit, // Subject line
    text: tit, // plain text body
    html: htmlData, // html body
  });

  console.log("Message sent: %s", info.messageId);
  // Message sent: <<EMAIL>>

  // Preview only available when sending through an Ethereal account
  console.log("Preview URL: %s", nodemailer.getTestMessageUrl(info));
  // Preview URL: https://ethereal.email/message/WaQKMgKddxQDoou...
}
// const htmlScript="<b>Hello world?</b>"
// mailer(htmlScript).catch(console.error);
const year = new Date().getFullYear();
const month = new Date().getMonth();
const day = new Date().getDate() - 1;
console.log(new Date());
const dateSche = new Date(year, month, day, 8, 59, 11)
  .toISOString()
  .split("T")[0];
// const scheduleTIme = new Date(year, month, day + 1, 00, 29, 00);
// console.log(scheduleTIme);
// schedule.scheduleJob(scheduleTIme, async function () {
//   console.log('The answer to life, the universe, and everything!')
//   await connection.query(
//     `call getPaymentDataCsv('${dateSche}')`,
//     async function (error, results) {
//       console.log(error, results)
//       if (results[0].length > 0 && results[0] != undefined) {
//         const title = `Payment Report on ${scheduleTIme}`
//         const htmlScript = `<p><b>Report Date: </b>${results[1][0].datefrom}</p>
//         <p><b>No of Payments : </b>${results[1][0].total}</p>
//         <p><b>Total Amount Recived : </b>${results[1][0].amount}</p>
//         <br />
//         <div>
//           <a
//             href="https://exams.navachaitanya.net/export-payment-data/${dateSche}"
//             style="
//               background-color: blue;
//               color: white;
//               padding: 10px;
//               border-radius: 20px;
//             "
//             >Download Excel File</a
//           >

//         </div>
//         `
//         mailer(htmlScript, title)
//         console.log(mailer(htmlScript, title))
//       } else {
//         const title = `Payment Report on ${scheduleTIme}`
//         const htmlScript = `<p><b>Report Date: </b>${results[1][0].datefrom}</p>
//         <p><b>No of Payments : </b>${results[1][0].total}</p>
//         <p><b>Total Amount Recived : </b>0</p>
//         <br />
//         <p>No Payments are done </p>
//         `
//         mailer(htmlScript, title)
//       }
//     }
//   )
// })

var compressfilesupload = multer({
  storage: storage,
  limits: { fileSize: 1024 * 1024 * 100 },
});
var reportstorage = multer.diskStorage({
  destination: async function (req, file, cb) {
    const { question } = req.headers;
    if (!fs.existsSync(`reported/${question}`)) {
      await fs.mkdirSync(`reported/${question}`);
    }
    const storeLink = "reported/" + question;
    cb(null, storeLink);
  },
  filename: function (req, file, cb) {
    const { linkfile } = req.headers;
    cb(null, linkfile);
  },
});
var reportcompressfilesupload = multer({
  storage: reportstorage,
});

// Multer configuration for PDF uploads to R2
var pdfStorage = multer.memoryStorage(); // Store in memory for direct upload to R2
var pdfUpload = multer({
  storage: pdfStorage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: function (req, file, cb) {
    // Only allow PDF files
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only PDF files are allowed!'), false);
    }
  }
});

// app.post(
//     '/upload-doc',
//     verifyJwt,
//     verifyAdmin,
//     compressfilesupload.array('file', 1),
//     async (req, res) => {
//         // new Promise((resolve, reject) => {
//         if (req.files) {
//             // console.log("test==>", req.files);
//             var options2 = {
//                 styleMap: ['u => strong']
//             }
//             await mammoth
//                 .convertToHtml({ path: req.files[0].path }, options2)
//                 .then(async function (result) {
//                     var data = result.value
//                     console.log(data)
//                     const typeofAction = req.headers['typeofaction']
//                     if (typeofAction === 'questions') {
//                         try {
//                             const allcategoryQ = await data.split('CATEGORY:').slice(1)
//                             const allQuestions = []
//                             var errorCheck = ""
//                             for (let each of allcategoryQ) {
//                                 const categroyName = each
//                                     .split(/Q:[0-9]+\)/)[0]
//                                     .split(',')[0]
//                                     .toString()
//                                     .trim()
//                                 const eachQUestion = each
//                                     .split(/Q:[0-9]+\)/)
//                                     .slice(1)
//                                     .map(eachQ => {
//                                         let question = eachQ.split(/[A-Z]:\)/)[0]
//                                         question = question.replace("'s", '`s');
//                                         question = question.replace("�s", '`s');
//                                         const options = eachQ
//                                             .split(/[A-Z]:\)/)
//                                             .slice(1)
//                                             .map((q, i) =>
//                                             // q.includes("<strong>") ? '<p>' + q.replace("<p>", '') + '$$1' : '<p>' + q.replace("<p>", '') + '$$0')
//                                             {
//                                                 if (q.includes('</strong>')) {
//                                                     let op =
//                                                         '<p>' + q.replace('<p>', '') + '</p>' + '$$1'
//                                                     op = op.replace('<strong>', '')
//                                                     op = op.replace('</strong>', '')
//                                                     op = op.replace('</p>', '')
//                                                     op = op.replace("'s", '`s');
//                                                     op = op.replace("�s", '`s');
//                                                     return op
//                                                 } else {
//                                                     let op =
//                                                         '<p>' + q.replace('<p>', '') + '</p>' + '$$0'
//                                                     op = op.replace('<strong>', '')
//                                                     op = op.replace('</strong>', '')
//                                                     op = op.replace('</p>', '')
//                                                     op = op.replace("'s", '`s');
//                                                     op = op.replace("�s", '`s');
//                                                     return op
//                                                 }
//                                             }
//                                             )
//                                         return {
//                                             Q: '<p>' + question,
//                                             options,
//                                             categoryId: parseInt(categroyName.trim())
//                                         }
//                                     })

//                                 for (let eachQUestionInf of eachQUestion) {
//                                     const { Q, options, categoryId } = eachQUestionInf
//                                     await connection.query(
//                                         `call spAdminUploadQuestions('Question','${Q}','',${categoryId})`,
//                                         async function (error, results) {
//                                             if (error) {
//                                                 console.log(error, "questions")
//                                             }
//                                             for (let eachOption of options) {
//                                                 let opti = eachOption
//                                                 let eachOP = opti.toString()
//                                                 // console.log(`call spAdminUploadQuestions('Options','${eachOP.split("$$")[0]}','${eachOP.split("$$")[1]}',${results[0][0].qID})`)
//                                                 await connection.query(
//                                                     `call spAdminUploadQuestions('Options','${eachOP
//                                                         .split('$$')[0]
//                                                         .replace('<strong>', '')
//                                                         .replace('</strong>', '')}','${eachOP.split('$$')[1]
//                                                     }',${results[0][0].qID})`,
//                                                     async function (error, results) {
//                                                         // if (error) {
//                                                         //   errorCheck =errorCheck+"        "+error.sqlMessage +"        "
//                                                         // }
//                                                     }
//                                                 )
//                                             }
//                                         }

//                                     )
//                                 }
//                                 // setTimeout(() => {
//                                 //   if (errorCheck === "" && i === allcategoryQ.length-1) {
//                                 //     resolve()
//                                 //   } else {
//                                 //     return res.send({ text: true, sqlMessage:errorCheck})
//                                 //   }
//                                 // }, 200)
//                             }
//                         } catch (error) {

//                         }

//                         // res.send({text:false});
//                         // setTimeout(()=>cb(req,res),200)
//                     } else if (typeofAction === 'Import Exams') {
//                         let content = data.split('<p>Q:)')
//                         for (let i = 1; i < content.length; i++) {
//                             let each = content[i]
//                             let eachCon = each.split('</p><p>')
//                             let examName = eachCon[0].split('EXAMNAME:')[1]
//                             let eDescription = eachCon[1].split('EXAMDESCRIPTION:')[1]
//                             let egids = eachCon[2]
//                                 .split('EXAMGIDS:')[1]
//                                 .replace('</p>', '')
//                                 .replace('<p>', '')
//                                 .trim()
//                             let estartDate = eachCon[3]
//                                 .split('EXAMSTARTDATE:')[1]
//                                 .replace('</p>', '')
//                                 .replace('<p>', '')
//                                 .trim()
//                             let endDate =
//                                 new Date().getFullYear() +
//                                 parseInt(10) +
//                                 '-' +
//                                 (parseInt(new Date().getMonth()) + parseInt(1)) +
//                                 '-' +
//                                 parseInt(new Date().getDate()) +
//                                 ' 09:00:00'
//                             console.log(
//                                 `call importQuiz('${examName}','${eDescription}','${estartDate}','${endDate}','${egids}')`
//                             )
//                             await connection.query(
//                                 `call importQuiz('${examName}','${eDescription}','${estartDate}','${endDate}','${egids}')`,
//                                 async function (error, results) {
//                                     if (results) return true
//                                     if (error) {
//                                         errorCheck = errorCheck + "        " + error.sqlMessage + "        "
//                                     }
//                                 }
//                             )
//                             // setTimeout(() => {
//                             //   if (errorCheck === "" && i === content.length-1) {
//                             //     resolve()
//                             //   } else {
//                             //     return res.send({ text: true, sqlMessage:errorCheck})
//                             //   }
//                             // }, 200)
//                         }

//                         // setTimeout(()=>cb(req,res),200)
//                     } else if (typeofAction === 'Add Qids to Exams') {
//                         let content = data.split('<p>Q:)')
//                         for (let i = 1; i < content.length; i++) {
//                             let eachIDS = content[i]
//                             let qids = eachIDS
//                                 .split('QIDS:')[1]
//                                 .replace('<p>', '')
//                                 .replace('</p>', '')
//                             let examid = eachIDS
//                                 .split('</p><p>')[0]
//                                 .split('EXAMID:')[1]
//                                 .replace('<p>', '')
//                                 .replace('</p>', '')
//                             console.log(`CALL importGetQids('${qids}')`)
//                             await connection.query(
//                                 `CALL importGetQids('${qids}')`,
//                                 async function (err, resu) {
//                                     let questionIds = resu[0][0].result
//                                     questionIds = questionIds.split(',').slice(0, -1).join(',')
//                                     await connection.query(
//                                         `call importQidsIntoQuiz(${examid},'${questionIds}',${questionIds.split(',').length
//                                         })`,
//                                         async function (error, results) {
//                                             // if (error) {
//                                             //   errorCheck =errorCheck+"        "+error.sqlMessage +"        "
//                                             // }
//                                         }
//                                     )
//                                 }
//                             )
//                             // setTimeout(() => {
//                             //   if (errorCheck === "" && i === content.length-1) {
//                             //     resolve()
//                             //   } else {
//                             //     return res.send({ text: true, sqlMessage:errorCheck})
//                             //   }
//                             // }, 200)
//                         }
//                         // resolve()
//                         // setTimeout(()=>cb(req,res),200)
//                     }
//                     try {
//                         fs.unlinkSync(req.files[0].path)
//                         // cb(req, res)
//                     } catch (er) { }
//                     // res.send(true)
//                 })
//                 .done()
//             return res.send({ text: false });
//         }
//         // }).then(() => res.send({ text: false }))
//         // uploadDoc(req, res).then(()=> res.send({ text: false }))
//     }
// )
app.post(
  "/upload-doc",
  verifyJwt,
  verifyAdmin,
  compressfilesupload.array("file", 1),
  async (req, res) => {
    // new Promise((resolve, reject) => {
    if (req.files) {
      // console.log("test==>", req.files);
      var options2 = {
        styleMap: ["u => strong"],
      };
      mammoth
        .convertToHtml({ path: req.files[0].path }, options2)
        .then(async function (result) {
          var data = result.value;
          const typeofAction = req.headers["typeofaction"];
          if (typeofAction === "questions") {
            try {
              const allcategoryQ = await data.split("CATEGORY:").slice(1);
              const allQuestions = [];
              var errorCheck = "";
              for (let each of allcategoryQ) {
                const categroyName = each
                  .split(/Q:[0-9]+\)/)[0]
                  .split(",")[0]
                  .toString()
                  .trim();
                const eachQUestion = each
                  .split(/Q:[0-9]+\)/)
                  .slice(1)
                  .map((eachQ) => {
                    let question = eachQ.split(/[A-Z]:\)/)[0];
                    question = question.replace("'s", "`s");
                    question = question.replace("�s", "`s");
                    const options = eachQ
                      .split(/[A-Z]:\)/)
                      .slice(1)
                      .map((q, i) =>
                        // q.includes("<strong>") ? '<p>' + q.replace("<p>", '') + '$$1' : '<p>' + q.replace("<p>", '') + '$$0')
                        {
                          if (q.includes("</strong>")) {
                            let op =
                              "<p>" + q.replace("<p>", "") + "</p>" + "$$1";
                            op = op.replace("<strong>", "");
                            op = op.replace("</strong>", "");
                            op = op.replace("</p>", "");
                            op = op.replace("'s", "`s");
                            op = op.replace("�s", "`s");
                            return op;
                          } else {
                            let op =
                              "<p>" + q.replace("<p>", "") + "</p>" + "$$0";
                            op = op.replace("<strong>", "");
                            op = op.replace("</strong>", "");
                            op = op.replace("</p>", "");
                            op = op.replace("'s", "`s");
                            op = op.replace("�s", "`s");
                            return op;
                          }
                        }
                      );
                    return {
                      Q: "<p>" + question,
                      options,
                      categoryId: parseInt(categroyName.trim()),
                    };
                  });

                for (let eachQUestionInf of eachQUestion) {
                  const { Q, options, categoryId } = eachQUestionInf;
                  await connection.query(
                    `call spAdminUploadQuestions('Question','${Q}','',${categoryId})`,
                    async function (error, results) {
                      if (error) {
                        console.log(error, "questions");
                      }
                      for (let eachOption of options) {
                        let opti = eachOption;
                        let eachOP = opti.toString();
                        // console.log(`call spAdminUploadQuestions('Options','${eachOP.split("$$")[0]}','${eachOP.split("$$")[1]}',${results[0][0].qID})`)
                        await connection.query(
                          `call spAdminUploadQuestions('Options','${eachOP
                            .split("$$")[0]
                            .replace("<strong>", "")
                            .replace("</strong>", "")}','${
                            eachOP.split("$$")[1]
                          }',${results[0][0].qID})`,
                          async function (error, results) {
                            // if (error) {
                            //   errorCheck =errorCheck+"        "+error.sqlMessage +"        "
                            // }
                          }
                        );
                      }
                    }
                  );
                }
                // setTimeout(() => {
                //   if (errorCheck === "" && i === allcategoryQ.length-1) {
                //     resolve()
                //   } else {
                //     return res.send({ text: true, sqlMessage:errorCheck})
                //   }
                // }, 200)
              }
            } catch (error) {}

            // setTimeout(()=>cb(req,res),200)
          } else if (typeofAction === "Import Exams") {
            // let content = data.split('<p>Q:)')
            // for (let i = 1; i < content.length; i++) {
            //     let each = content[i]
            //     let eachCon = each.split('</p><p>')
            //     let examName = eachCon[0].split('EXAMNAME:')[1]
            //     let eDescription = eachCon[1].split('EXAMDESCRIPTION:')[1]
            //     let egids = eachCon[2]
            //         .split('EXAMGIDS:')[1]
            //         .replace('</p>', '')
            //         .replace('<p>', '')
            //         .trim()
            //     let estartDate = eachCon[3]
            //         .split('EXAMSTARTDATE:')[1]
            //         .replace('</p>', '')
            //         .replace('<p>', '')
            //         .trim()
            //     let endDate =
            //         new Date().getFullYear() +
            //         parseInt(10) +
            //         '-' +
            //         (parseInt(new Date().getMonth()) + parseInt(1)) +
            //         '-' +
            //         parseInt(new Date().getDate()) +
            //         ' 09:00:00'
            //     console.log(
            //         `call importQuiz('${examName}','${eDescription}','${estartDate}','${endDate}','${egids}')`
            //     )
            //     await connection.query(
            //         `call importQuiz('${examName}','${eDescription}','${estartDate}','${endDate}','${egids}')`,
            //         async function (error, results) {
            //             if (results) return true
            //             if (error) {
            //                 errorCheck = errorCheck + "        " + error.sqlMessage + "        "
            //             }
            //         }
            //     )
            //     // setTimeout(() => {
            //     //   if (errorCheck === "" && i === content.length-1) {
            //     //     resolve()
            //     //   } else {
            //     //     return res.send({ text: true, sqlMessage:errorCheck})
            //     //   }
            //     // }, 200)
            // }
            // try {
            //     const examInfo2 = await data.split('CATEGORY:')[0]
            //     const examName = examInfo2?.split("ExamName:")[1].split("Syllabus:")[0]
            //     const syllabus = examInfo2?.split("Syllabus:")[1].split("StartDate:")[0]
            //     const startdate = examInfo2?.split("StartDate:")[1].split("EndDate:")[0].replace("<p>", "").replace("</p>", "").trim()
            //     const endDate = examInfo2?.split("EndDate:")[1].split("AssigntoGroup:")[0].replace("<p>", "").replace("</p>", "").trim()
            //     const assignToGroup = examInfo2?.split("AssigntoGroup:")[1].replace("<p>", "").replace("</p>", "").trim()
            //     await connection.query(
            //         `call importQuiz('${examName}','${syllabus}','${startdate}','${endDate}','${assignToGroup}')`,
            //         async function (error, results) {
            //             if (results) {
            //                 const examId = results[0][0].qid
            //                 const allcategoryQ = await data.split('CATEGORY:').slice(1)
            //                 const allQuestions = []
            //                 var errorCheck = ""
            //                 for (let each of allcategoryQ) {
            //                     const categroyName = each
            //                         .split(/Q:[0-9]+\)/)[0]
            //                         .split(',')[0]
            //                         .toString()
            //                         .trim()
            //                     const eachQUestion = each
            //                         .split(/Q:[0-9]+\)/)
            //                         .slice(1)
            //                         .map(eachQ => {
            //                             let question = eachQ.split(/[A-Z]:\)/)[0]
            //                             question = question.replace("'s", '`s');
            //                             question = question.replace("�s", '`s');
            //                             const options = eachQ
            //                                 .split(/[A-Z]:\)/)
            //                                 .slice(1)
            //                                 .map((q, i) =>
            //                                 // q.includes("<strong>") ? '<p>' + q.replace("<p>", '') + '$$1' : '<p>' + q.replace("<p>", '') + '$$0')
            //                                 {
            //                                     if (q.includes('</strong>')) {
            //                                         let op =
            //                                             '<p>' + q.replace('<p>', '') + '</p>' + '$$1'
            //                                         op = op.replace('<strong>', '')
            //                                         op = op.replace('</strong>', '')
            //                                         op = op.replace('</p>', '')
            //                                         op = op.replace("'s", '`s');
            //                                         op = op.replace("�s", '`s');
            //                                         return op
            //                                     } else {
            //                                         let op =
            //                                             '<p>' + q.replace('<p>', '') + '</p>' + '$$0'
            //                                         op = op.replace('<strong>', '')
            //                                         op = op.replace('</strong>', '')
            //                                         op = op.replace('</p>', '')
            //                                         op = op.replace("'s", '`s');
            //                                         op = op.replace("�s", '`s');
            //                                         return op
            //                                     }
            //                                 }
            //                                 )
            //                             return {
            //                                 Q: '<p>' + question,
            //                                 options,
            //                                 categoryId: parseInt(categroyName.trim())
            //                             }
            //                         })

            //                     for (let eachQUestionInf of eachQUestion) {
            //                         const { Q, options, categoryId } = eachQUestionInf
            //                         await connection.query(
            //                             `call spAdminUploadQuestions('Question','${Q}','${examId}',${categoryId})`,
            //                             async function (error, results) {
            //                                 if (error) {
            //                                     console.log(error, "questions")
            //                                 }
            //                                 for (let eachOption of options) {
            //                                     let opti = eachOption
            //                                     let eachOP = opti.toString()
            //                                     // console.log(`call spAdminUploadQuestions('Options','${eachOP.split("$$")[0]}','${eachOP.split("$$")[1]}',${results[0][0].qID})`)
            //                                     await connection.query(
            //                                         `call spAdminUploadQuestions('Options','${eachOP
            //                                             .split('$$')[0]
            //                                             .replace('<strong>', '')
            //                                             .replace('</strong>', '')}','${eachOP.split('$$')[1]
            //                                         }',${results[0][0].qID})`,
            //                                         async function (error, results) {
            //                                             // if (error) {
            //                                             //   errorCheck =errorCheck+"        "+error.sqlMessage +"        "
            //                                             // }
            //                                         }
            //                                     )
            //                                 }
            //                             }

            //                         )
            //                     }
            //                     // setTimeout(() => {
            //                     //   if (errorCheck === "" && i === allcategoryQ.length-1) {
            //                     //     resolve()
            //                     //   } else {
            //                     //     return res.send({ text: true, sqlMessage:errorCheck})
            //                     //   }
            //                     // }, 200)
            //                 }
            //             }
            //             if (error) {
            //                 errorCheck = errorCheck + "        " + error.sqlMessage + "        "
            //             }
            //         }
            //     )

            // } catch (error) {
            // }
            try {
              const allExams = await data.split("Exam:").slice(1);
              for (const eachExam of allExams) {
                const examInfo2 = await eachExam.split("CATEGORY:")[0];
                const examName = examInfo2
                  ?.split("ExamName:")[1]
                  .split("Syllabus:")[0];
                const syllabus = examInfo2
                  ?.split("Syllabus:")[1]
                  .split("StartDate:")[0];
                const startdate = examInfo2
                  ?.split("StartDate:")[1]
                  .split("EndDate:")[0]
                  .replace("<p>", "")
                  .replace("</p>", "")
                  .trim();
                const endDate = examInfo2
                  ?.split("EndDate:")[1]
                  .split("AssigntoGroup:")[0]
                  .replace("<p>", "")
                  .replace("</p>", "")
                  .trim();
                const assignToGroup = examInfo2
                  ?.split("AssigntoGroup:")[1]
                  .split("Day:")[0]
                  .replace("<p>", "")
                  .replace("</p>", "")
                  .trim();
                const day = examInfo2
                  ?.split("Day:")[1]
                  .replace("<p>", "")
                  .replace("</p>", "")
                  .trim();
                await connection.query(
                  `call importQuiz('${examName}','${syllabus}','${startdate}','${endDate}','${assignToGroup}')`,
                  async function (error, results) {
                    if (results) {
                      const examId = results[0][0].qid;
                      const allcategoryQ = await eachExam
                        .split("CATEGORY:")
                        .slice(1);
                      const allQuestions = [];
                      var errorCheck = "";
                      await connection.query(
                        `UPDATE savsoft_quiz
                        set day='${day}'
                        WHERE quid = '${examId}'
                        `,
                        async function (error, results) {}
                      );
                      for (let each of allcategoryQ) {
                        const categroyName = each
                          .split(/Q:[0-9]+\)/)[0]
                          .split(",")[0]
                          .toString()
                          .trim();
                        const eachQUestion = each
                          .split(/Q:[0-9]+\)/)
                          .slice(1)
                          .map((eachQ) => {
                            let question = eachQ.split(/[A-Z]:\)/)[0];
                            question = question.replace("'s", "`s");
                            question = question.replace("�s", "`s");
                            const options = eachQ
                              .split(/[A-Z]:\)/)
                              .slice(1)
                              .map((q, i) =>
                                // q.includes("<strong>") ? '<p>' + q.replace("<p>", '') + '$$1' : '<p>' + q.replace("<p>", '') + '$$0')
                                {
                                  if (q.includes("</strong>")) {
                                    let op =
                                      "<p>" +
                                      q.replace("<p>", "") +
                                      "</p>" +
                                      "$$1";
                                    op = op.replace("<strong>", "");
                                    op = op.replace("</strong>", "");
                                    op = op.replace("</p>", "");
                                    op = op.replace("'s", "`s");
                                    op = op.replace("�s", "`s");
                                    return op;
                                  } else {
                                    let op =
                                      "<p>" +
                                      q.replace("<p>", "") +
                                      "</p>" +
                                      "$$0";
                                    op = op.replace("<strong>", "");
                                    op = op.replace("</strong>", "");
                                    op = op.replace("</p>", "");
                                    op = op.replace("'s", "`s");
                                    op = op.replace("�s", "`s");
                                    return op;
                                  }
                                }
                              );
                            return {
                              Q: "<p>" + question,
                              options,
                              categoryId: parseInt(categroyName.trim()),
                            };
                          });

                        for (let eachQUestionInf of eachQUestion) {
                          console.log(eachQUestionInf, "eachQUestionInf ");
                          const { Q, options, categoryId } = eachQUestionInf;
                          await connection.query(
                            `call spAdminUploadQuestions('Question','${Q}','${examId}',${categoryId})`,
                            async function (error, results) {
                              if (error) {
                                console.log(error, "questions");
                              }
                              for (let eachOption of options) {
                                let opti = eachOption;
                                let eachOP = opti.toString();
                                // console.log(`call spAdminUploadQuestions('Options','${eachOP.split("$$")[0]}','${eachOP.split("$$")[1]}',${results[0][0].qID})`)
                                await connection.query(
                                  `call spAdminUploadQuestions('Options','${eachOP
                                    .split("$$")[0]
                                    .replace("<strong>", "")
                                    .replace("</strong>", "")}','${
                                    eachOP.split("$$")[1]
                                  }',${results[0][0].qID})`,
                                  async function (error, results) {
                                    // if (error) {
                                    //   errorCheck =errorCheck+"        "+error.sqlMessage +"        "
                                    // }
                                  }
                                );
                              }
                            }
                          );
                        }
                        // setTimeout(() => {
                        //   if (errorCheck === "" && i === allcategoryQ.length-1) {
                        //     resolve()
                        //   } else {
                        //     return res.send({ text: true, sqlMessage:errorCheck})
                        //   }
                        // }, 200)
                      }
                    }
                    if (error) {
                      errorCheck =
                        errorCheck + "        " + error.sqlMessage + "        ";
                    }
                  }
                );
              }
            } catch (error) {}
            // setTimeout(()=>cb(req,res),200)
          } else if (typeofAction === "Add Qids to Exams") {
            let content = data.split("<p>Q:)");
            for (let i = 1; i < content.length; i++) {
              let eachIDS = content[i];
              let qids = eachIDS
                .split("QIDS:")[1]
                .replace("<p>", "")
                .replace("</p>", "");
              let examid = eachIDS
                .split("</p><p>")[0]
                .split("EXAMID:")[1]
                .replace("<p>", "")
                .replace("</p>", "");
              console.log(`CALL importGetQids('${qids}')`);
              await connection.query(
                `CALL importGetQids('${qids}')`,
                async function (err, resu) {
                  let questionIds = resu[0][0].result;
                  questionIds = questionIds.split(",").slice(0, -1).join(",");
                  await connection.query(
                    `call importQidsIntoQuiz(${examid},'${questionIds}',${
                      questionIds.split(",").length
                    })`,
                    async function (error, results) {
                      // if (error) {
                      //   errorCheck =errorCheck+"        "+error.sqlMessage +"        "
                      // }
                    }
                  );
                }
              );
              // setTimeout(() => {
              //   if (errorCheck === "" && i === content.length-1) {
              //     resolve()
              //   } else {
              //     return res.send({ text: true, sqlMessage:errorCheck})
              //   }
              // }, 200)
            }
            // resolve()
            // setTimeout(()=>cb(req,res),200)
          }
          try {
            fs.unlinkSync(req.files[0].path);
            // cb(req, res)
          } catch (er) {}
          res.send(true);
        })
        .done();
      // return res.send({text:false});
    }
    // }).then(() => res.send({ text: false }))
    // uploadDoc(req, res).then(()=> res.send({ text: false }))
  }
);
app.post(
  "/report-upload-image",
  verifyJwt,
  reportcompressfilesupload.single("file"),
  async (req, res) => {
    try {
      if (req.file) {
        const { question, linkfile } = req.headers;
        const { mimetype, size } = req.file;
        // if (question === "whatsapp") {
        //   const config = {
        //     method: "post",
        //     url: `https://graph.facebook.com/v18.0/1090076848664285/uploads?file_length=${size}&file_type=${mimetype}`,
        //     headers: {
        //       Authorization: `Bearer EAAPfa0tXrt0BO0htZBohZCG5nzQzQyHoJhm5tbF48nZBNyTi0cifS0KVb4Iz4elZCTTwP6bi3Igho7ZCss1qf7ZB7WHyQlqPcZBI6LlkyjlZBxuKP5CCjljOALjkjTy4WR9anfRrZB9zaYC77ZCRstYjsPr5uaIyZAWj5orckwgR7q7YLJiZCKg21G5UxtDpZByexoSQCx8X3ZA8f9yxsd3zBT`,
        //       "Content-Type": "application/json",
        //     },
        //     data: {},
        //   };

        //   const response = await axios(config);
        //   if (response) {
        //     const uploadId = response?.data?.id;
        //     const fileContent = fs.readFileSync(req.file.path, "utf-8");

        //     let config = {
        //       method: "post",
        //       maxBodyLength: Infinity,
        //       url: `https://graph.facebook.com/v18.0/${uploadId}`,
        //       headers: {
        //         Authorization:
        //           "OAuth EAAPfa0tXrt0BO0htZBohZCG5nzQzQyHoJhm5tbF48nZBNyTi0cifS0KVb4Iz4elZCTTwP6bi3Igho7ZCss1qf7ZB7WHyQlqPcZBI6LlkyjlZBxuKP5CCjljOALjkjTy4WR9anfRrZB9zaYC77ZCRstYjsPr5uaIyZAWj5orckwgR7q7YLJiZCKg21G5UxtDpZByexoSQCx8X3ZA8f9yxsd3zBT",
        //         "Content-Type": mimetype,
        //         Cookie: "ps_l=0; ps_n=0",
        //       },
        //       data: fileContent,
        //     };
        //     const response2 = await axios(config);
        //     try {
        //       fs.unlinkSync(req.file.path);
        //     } catch (error) {}

        //     res.send(response2.data?.h);
        //     return;
        //   }
        // }
        res.send("file uploaded successfully");
      }
    } catch (error) {}
  }
);

app.get("/quiz_details/:quid", verifyJwt, async (request, response) => {
  const { quid } = request.params;
  const { phone } = request;
  console.log(phone);
  await connection.query(
    `call getquiz('${quid}','${phone}')`,
    async function (error, results) {
      if (error) response.send(error);
      response.send(JSON.stringify([...results, ...[{ phone: phone }]]));
    }
  );
});

app.get("/all-result-list/", verifyJwt, async (request, response) => {
  const { phone } = request;
  console.log(phone);
  await connection.query(
    "call getallresults(" + phone + ")",
    async function (error, results) {
      if (error) response.send(error);
      response.send(JSON.stringify(results));
    }
  );
});

app.get("/all-exam-schedules", verifyJwt, async (request, response) => {
  const { phone } = request;
  console.log(phone);
  await connection.query(
    "call getExamSchedules()",
    async function (error, results) {
      if (error) response.send(error);
      response.send(JSON.stringify(results));
    }
  );
});

app.post("/update-user-details", verifyJwt, async (request, response) => {
  const { phone } = request;
  const { type } = request.body;
  await connection.query(
    `CALL spGetEditUser('${phone}','${type}')`,
    async function (error, results) {
      if (error) response.send(error);
      response.send(results);
    }
  );
});

app.post(
  "/report-question/:quizId/:qid",
  verifyJwt,
  async (request, response) => {
    const { phone } = request;
    const { msg } = request.body;
    const { quizId, qid } = request.params;
    const { fileurl } = request.headers;

    await connection.query(
      `CALL reportQuestion('${phone}',${quizId},${qid},'${msg}','${fileurl}')`,
      async function (error, results) {
        if (error) response.send(error);
        console.log(results[0][0].result);
        const reportMsg = results[0][0].result;
        if (reportMsg !== "error") {
          const title = "New Question Reported By Student...";
          const htmlScript = `<p><b>Exam Name : </b>${results[0][0].quiz_name}</p>
        <p><b>Reported by :</b> ${phone}</p>
        <br />
        <div>
          <a
            href="https://exams.navachaitanya.net/admin/qbank/reported-questions/${quizId}/${results[0][0].id}"
            style="
              background-color: blue;
              color: white;
              padding: 10px;
              border-radius: 20px;
            "
            >Edit Question</a
          >
        
          <a
            href="${fileurl}"
            style="
              background-color: blue;
              color: white;
              padding: 10px;
              border-radius: 20px;
            "
            >Check proof Image</a
          >
        </div>
        `;
          mailer(htmlScript, title).catch(console.error);
        }
        response.send(results);
      }
    );
  }
);

app.post("/verify-user", async (request, response) => {
  const { number } = request.body;
  await connection.query(
    `SELECT * FROM savsoft_users Where contact_no=${number}`,
    async function (error, results) {
      if (error) response.send(error);
      response.send(results);
    }
  );
});
app.post("/get-group-quizzes/:guid", verifyJwt, async (request, response) => {
  const { guid } = request.params;
  const { phone } = request;
  console.log(request.phone);
  await connection.query(
    `CALL getGroupQuizList('${phone}','${guid}')`,
    async function (error, results) {
      if (error) response.send(error);
      response.send(results);
    }
  );
});
app.post("/get-group-details/:gid", async (req, res) => {
  const { gid } = req.params;
  await connection.query(
    `SELECT gr.*,scheduleLink FROM savsoft_group gr LEFT JOIN
    (	SELECT gid,drive_link scheduleLink
          from
    t_exam_schedules 
    WHERE category='schedule') sch on sch.gid=gr.gid
    where gr.gid=${gid}`,
    async function (error, results) {
      // res.send(error);
      res.send(results);
    }
  );
});
app.post("/generate-user-token", async (request, response) => {
  const { number } = request.body;

  await connection.query(
    `SELECT * FROM savsoft_users Where contact_no=${number}`,
    async function (error, results) {
      if (error) response.send(error);
      var token = await jwt.sign({ number }, "nookenavanishincexams");
      response.send({ result: results, jwt: token });
    }
  );
});

app.post("/create-new-user", async (request, response) => {
  const { name, surname, email, district, dob, number, type } = request.body;
  if (type === "CREATE") {
    let token = "";
    await connection.query(
      `CALL createUpdateUser('${name}', '${email}', '${surname}', '${district}','${dob}', '${number}','${type}')`,
      async function (err, resu) {
        if (err) response.send(err);
        token = await jwt.sign({ number }, "nookenavanishincexams");
      }
    );
    storeINExcel(name, number, "New User From Website", false);

    await connection.query(
      `SELECT * FROM savsoft_users Where contact_no=${number}`,
      async function (error, results) {
        if (error) response.send(error);
        response.send({ result: results, jwt: token });
      }
    );
    axios.post(
      `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
      {
        businessId: 1,
        verifyToken: Math.random() * 15000,
        phoneNumber: number,
        message: [],
        messageType: "promotion",
        templateLang: "te",
        templateName: "user_reg_sucess_message",
      }
    );
  } else {
    await connection.query(
      `CALL createUpdateUser('${name}', '${email}', '${surname}', '${district}','${dob}', '${number}','${type}')`,
      async function (err, resu) {
        if (err) response.send(err);
        response.send(resu);
      }
    );
  }
});

app.post("/get-user-details/:uid", async (request, response) => {
  const { uid } = request.params;
  await connection.query(
    `CALL getUserInfo('${uid}')`,
    async function (error, results) {
      if (error) response.send(error);
      response.send({ result: results[0] });
    }
  );
});

app.get("/get-packages-list", async (request, response) => {
  await connection.query(
    "call getUserPackages()",
    async function (error, results) {
      if (error) response.send(error);
      response.send(results[0]);
      // connection.release();
    }
  );
});

app.post("/all-test-results/view", async (request, response) => {
  const { quid } = request.params;
  // const quizNo = parseInt(quid);
  // response.send(quid);

  await connection.query(
    "SELECT * FROM test_result order by rid desc",
    async function (error, results) {
      if (error) response.send(error);

      response.send(JSON.stringify(results));
    }
  );
});
app.get(
  "/view-result/:phone/:quid/:rid",
  verifyJwt,
  async (request, response) => {
    const { quid, rid, phone } = request.params;
    await connection.query(
      `call getResult(${quid}, ${rid},'${phone}')`,
      async function (error, results) {
        if (error) response.send(error);
        response.send(JSON.stringify(results));
      }
    );
  }
);

app.post(
  "/quiz-attempt/submit-quiz/:quid",
  verifyJwt,
  async (request, response) => {
    const { quid } = request.params;
    const { phone } = request;
    const { starttime, endtime, scoreTotal, scorei } = request.body;
    // response.send(quid);

    await connection.query(
      `CALL storeAnswers(${quid},${scoreTotal},'${scorei}','${starttime}','${endtime}','${phone}')`,
      async function (error, results) {
        if (error) response.send(error);
        response.send(JSON.stringify(results[0]));
      }
    );
  }
);

app.post("/payment_gateway/payumoney/success", async (req, res) => {
  const {
    status,
    firstname,
    net_amount_debit,
    phone,
    productinfo,
    txnid,
    addedon,
  } = req.body;
  console.log(
    `CALL storePaymentDetails('${status}','${firstname}','${net_amount_debit}',${phone},'${txnid}','${addedon}','${
      productinfo.split("date")[0]
    }','${productinfo.split("date")[1]}')`
  );
  const title = `Payment received for amount Rs. ${net_amount_debit}`;
  const htmlScript = `<p><b>Paid Date: </b>${addedon}</p>
        <p><b>Transaction Id : </b>${txnid}</p>
        <p><b>Student Name : </b>${phone}</p>
        <p><b>Student Phone Number : </b>${firstname}</p>
        <br />
        <br />
        Team,
        NavaCHAITANYA Competitions LLP...
        `;
  mailer(htmlScript, title);
  console.log(mailer(htmlScript, title));
  await connection.query(
    `CALL storePaymentDetails('${status}','${firstname}','${net_amount_debit}',${phone},'${txnid}','${addedon}','${
      productinfo.split("date")[0]
    }','${productinfo.split("date")[1]}')`,

    async function (error, results) {
      if (results) return res.send(results);
      // return res.redirect(
      //   `http://localhost:3000/exams-list/${productinfo}?message="Your Package Activated Successfully.."`
      // );
    }
  );
  connection.query(
    `CALL supportHome('getOnsignalId', '${phone}','0')`,
    async function (err, resu) {
      if (resu.length > 0) {
        let allSgnalIds = {};
        let count = 1;
        let users = 1;
        for (let each of resu[0]) {
          if (each.onesignalId) {
            if (!allSgnalIds[count]) {
              allSgnalIds[count] = []; // Initialize as an array if not already
            }
            allSgnalIds[count].push(each.onesignalId);
            users++;
          }
          if (users === 1999) {
            count++;
          }
        }

        const allUsers = Object.values(allSgnalIds);
        for (let each of allUsers) {
          await axios.post(`https://api.onesignal.com/notifications`, {
            app_id: "4f2fd23f-377c-40be-8cca-7867d0e6fdd6",
            name: "string",
            contents: {
              en: "Start practice now.",
            },
            headings: {
              en: "Your Package Activated Successfully..",
            },
            url: 'https://exams.navachaitanya.net/?message="Your Package Activated Successfully..',
            include_player_ids: each,
          });
        }
      }
    }
  );
});
app.post("/payment_gateway/free/success", verifyJwt, async (req, res) => {
  const {
    status,
    firstname,
    net_amount_debit,
    phone,
    productinfo,
    txnid,
    addedon,
    validity,
    usedwalletbal,
  } = req.body;
  console.log(req.body, "req.body");
  await connection.query(
    `CALL storePaymentDetails('${status}','${firstname}','${net_amount_debit}',${phone},'${txnid}','${addedon}','${productinfo}','${validity}',${usedwalletbal})`,
    async function (error, results) {
      console.log("err", error);
      if (results) res.send(results);
    }
  );
  connection.query(
    `CALL supportHome('getOnsignalId', '${phone}','0')`,
    async function (err, resu) {
      if (resu.length > 0) {
        let allSgnalIds = {};
        let count = 1;
        let users = 1;
        for (let each of resu[0]) {
          if (each.onesignalId) {
            if (!allSgnalIds[count]) {
              allSgnalIds[count] = []; // Initialize as an array if not already
            }
            allSgnalIds[count].push(each.onesignalId);
            users++;
          }
          if (users === 1999) {
            count++;
          }
        }

        const allUsers = Object.values(allSgnalIds);
        for (let each of allUsers) {
          await axios.post(`https://api.onesignal.com/notifications`, {
            app_id: "4f2fd23f-377c-40be-8cca-7867d0e6fdd6",
            name: "string",
            contents: {
              en: "Start practice now.",
            },
            headings: {
              en: "Your Package Activated Successfully..",
            },
            url: 'https://exams.navachaitanya.net/?message="Your Package Activated Successfully..',
            include_player_ids: each,
          });
        }
      }
    }
  );
});

app.post("/payment_gateway/payumoney/fail", async (req, res) => {
  return res.redirect(
    `https://exams.navachaitanya.net/payment-failed?message="Payment Failed"`
  );
});

app.post("/get-group-details/:gid", async (req, res) => {
  const { gid } = req.params;
  await connection.query(
    `SELECT group_name,price,valid_for_days FROM savsoft_group where gid=${gid}`,
    async function (error, results) {
      // res.send(error);
      res.send(results);
    }
  );
});

app.post("/get-discount-coupon-details/:coupon/:gid", async (req, res) => {
  const { coupon, gid } = req.params;
  await connection.query(
    `SELECT * FROM discount_coupon  WHERE name='${coupon}' AND FIND_IN_SET('${gid}', groupIds)`,
    async function (error, results) {
      // res.send(error);
      res.send(results);
    }
  );
});

// Admin

app.get(
  "/dashboard/:page",
  verifyJwt,
  verifyAdmin,
  async (request, response) => {
    const { page } = request.params;
    console.log(page);
    await connection.query(
      `call getDashboardData(${parseInt(page) * 10})`,
      async function (error, results) {
        if (error) response.send(error);
        response.send(JSON.stringify(results));
      }
    );
  }
);

app.get(
  "/adminmasterdata/:type",
  verifyJwt,
  verifyAdmin,
  async (request, response) => {
    const { type } = request.params;
    await connection.query(
      `call adminMaster('${type}')`,
      async function (error, results) {
        if (error) response.send(error);
        response.send(JSON.stringify(results));
      }
    );
  }
);

app.post(
  "/admin/create-new-user",
  verifyJwt,
  verifyAdmin,
  async (request, response) => {
    const {
      name,
      surname,
      email,
      district,
      dob,
      number,
      type,
      su,
      gidselected,
    } = request.body;

    await connection.query(
      `CALL AdmincreateUpdateUser('${name}', '${email}', '${surname}', '${district}','${dob}', '${number}','${type}',${su},'${gidselected}')`,
      async function (err, resu) {
        if (err) response.send(err);
        response.send(resu);
      }
    );
    storeINExcel(name, number, "New User Created by Admin", false);

    axios.post(
      `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
      {
        businessId: 1,
        verifyToken: Math.random() * 15000,
        phoneNumber: number,
        message: [],
        messageType: "promotion",
        templateLang: "te",
        templateName: "user_reg_sucess_message",
      }
    );
  }
);

const deleteDirectory = async (dir) => {
  // delete directory recursively
  try {
    fs.rmdirSync(dir, { recursive: true });
    console.log(`${dir} is deleted!`);
  } catch (err) {
    console.error(`Error while deleting ${dir}.`);
  }
};
app.post(
  "/admin/qbankdata",
  verifyJwt,
  verifyAdmin,
  async (request, response) => {
    const { type, search, qid } = request.body;
    if (type === "updateQuestionsEnglishExcel") {
      const {
        qid: questionId,
        question,
        optionId1,
        option1Text,
        optionId2,
        option2Text,
        optionId3,
        option3Text,
        optionId4,
        option4Text,
      } = qid;
      await connection.query(`
        UPDATE savsoft_qbank q
        JOIN savsoft_options o ON q.qid = o.qid
        SET 
          q.question_in_english = CASE q.qid 
            WHEN '${questionId}' THEN '${question}'
          END, 
          o.option_in_english = CASE o.oid 
            WHEN '${optionId1}' THEN '${option1Text}'
            WHEN '${optionId2}' THEN '${option2Text}'
            WHEN '${optionId3}' THEN '${option3Text}'
            WHEN '${optionId4}' THEN '${option4Text}'
          END 
        WHERE q.qid IN ('${questionId}');
      `);
      return response.send(true);
    } else {
      await connection.query(
        `CALL adminQbankData('${type}', '${search}', '${qid}')`,
        async function (err, resu) {
          if (err) response.send(err);
          response.send(resu);
        }
      );
    }
    if (type === "reportedDelete") {
      deleteDirectory("reported/" + qid);
    }
  }
);

app.post("/support", async (request, response) => {
  const { type, search, qid } = request.body;
  await connection.query(
    `CALL supportHome('${type}', '${search}', '${qid}')`,
    async function (err, resu) {
      if (err) response.send(err);
      response.send(resu);
    }
  );
});

app.post(
  "/admin/add-edit-question",
  verifyJwt,
  verifyAdmin,
  async (request, response) => {
    let {
      questionText,
      op1,
      op2,
      op3,
      op4,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      selectedCat,
      correctOption,
      qNum,
      quizId,
      type,
      op5,
      op5Num,
    } = request.body;
    questionText = questionText.replace("'s", "`s");
    questionText = questionText.replace("�s", "`s");
    op1 = op1.replace("'s", "`s");
    op1 = op1.replace("�s", "`s");
    op2 = op2.replace("'s", "`s");
    op2 = op2.replace("�s", "`s");
    op3 = op3.replace("'s", "`s");
    op3 = op3.replace("�s", "`s");
    op4 = op4.replace("'s", "`s");
    op4 = op4.replace("�s", "`s");
    op5 = op5.replace("'s", "`s");
    op5 = op5.replace("�s", "`s");
    await connection.query(
      `CALL AdminAddEditQuestion('${type}','${questionText}', '${op1}', '${op2}', '${op3}','${op4}', '${selectedCat}','${correctOption}',${qNum},'${quizId}',${op1Num},${op2Num},${op3Num},${op4Num},'${op5}',${op5Num})`,
      async function (err, resu) {
        if (err) {
          response.send(err);
        } else {
          response.send(resu);
          if (String(type).split(",")[0] === "EDITREPORTED") {
            deleteDirectory("reported/" + qNum);
          }
        }
      }
    );
  }
);

app.post(
  "/admin/add-edit-group",
  verifyJwt,
  verifyAdmin,
  async (request, response) => {
    const {
      schedule,
      description,
      name,
      price,
      validity,
      groupOrder,
      groupId,
      enabled,
      type,
      syllabus,
    } = request.body;

    await connection.query(
      `CALL AdminAddEditPackage('${type}','${schedule}', '${description}', '${name}', ${price},${validity}, ${groupOrder},${groupOrder},${groupId},${enabled},'${syllabus}')`,
      async function (err, resu) {
        if (err) response.send(err);
        response.send(resu);
      }
    );
  }
);

app.post(
  "/admin/add-edit-exam",
  verifyJwt,
  verifyAdmin,
  async (request, response) => {
    const {
      type,
      examid,
      name,
      startdate,
      enddate,
      duration,
      attempts,
      selectedGids,
      description,
      selectedqids,
      noq,
      minPercent,
    } = request.body;

    await connection.query(
      `CALL AdminAddEditExams('${type}',${examid}, '${name}', '${startdate}', '${enddate}', '${duration}',${attempts}, '${selectedGids}','${description}','${selectedqids}',${noq},${minPercent})`,
      async function (err, resu) {
        if (err) response.send(err);
        response.send(resu);
      }
    );
  }
);

// app.get("/realtime-users", verifyJwt, verifyAdmin, async (re, res) => {
//   // Imports the Google Cloud client library.

//   const propertyId = "258958975";
//   // Imports the Google Analytics Data API client library.
//   const { BetaAnalyticsDataClient } = require("@google-analytics/data");

//   // Creates a client.
//   const analyticsDataClient = new BetaAnalyticsDataClient();

//   // Runs a realtime report.
//   try {
//     async function runRealtimeReport() {
//       const [response] = await analyticsDataClient.runRealtimeReport({
//         // The property parameter value must be in the form `properties/1234`
//         // where `1234` is a GA4 property Id.
//         property: `properties/${propertyId}`,
//         dimensions: [
//           {
//             name: "country",
//           },
//         ],
//         metrics: [
//           {
//             name: "activeUsers",
//           },
//         ],
//       });

//       console.log("Report result:");
//       response.rows.forEach((row) => {
//         console.log(row.dimensionValues[0], row.metricValues[0]);
//       });
//       res.send(JSON.stringify(response));
//     }
//     runRealtimeReport();
//   } catch (err) {
//     console.log(err);
//   }
// });
app.get("/export-payment-details/:date", async (request, response) => {
  const { date } = request.params;
  await connection.query(
    `call getPaymentDataCsv('${date}')`,
    async function (error, results) {
      if (error) response.send(error);
      response.send(results);
    }
  );
});
/*app.get("/download-pdf/:type/:quid/:text", async (request, response) => {
  const { quid, type } = request.params;
  var html = fs.readFileSync("public/templates/leaderboard.html", "utf8");
  var options = {
    format: "A4",
    orientation: "portrait",
    border: "0mm",
    // header: {
    //   height: "45mm",
    //   contents: '<div style="text-align: center;">Author: Shyam Hajare</div>',
    // },
    childProcessOptions: {
      env: {
        OPENSSL_CONF: "/dev/null",
      },
    },
  };
  // let data = null;
  const data = await connection.query(
    `call getPdfdata('${type}',${quid})`,
    async function (error, results) {
      if (error) console.log(error);
      console.log(results[0]);

      if (type === "questiondata") {
        const questions = [];
        results[1].forEach((element) => {
          questions.push({
            question: element.question,
            qNum: element.qNUm,
            options: element.options.split(",").map((op) => {
              op: op;
            }),
            correct: element.correct
              .split(",")
              .filter((e) => e.split("score")[1] == " 1]")
              ? element.correct
                  .split(",")
                  .filter((e) => e.split("score")[1] == " 1]")[0]
                  .split("score")[0]
                  .trim()
              : "",
          });
        });
        var document = {
          html: html,
          data: {
            questions: questions,
            header: results[0][0].quiz_name,
          },
          path: "public/leaderboard.pdf",
          type: "",
        };
        console.log(document.data);
      } else {
        var document = {
          html: html,
          data: {
            users: results[1],
            toppers: results[1].slice(0, 3),
            header: results[0][0].name,
          },
          path: "public/leaderboard.pdf",
          type: "",
        };
      }

      //   console.log(data)
      var options2 = {
        root: path.join(__dirname + "/public/"),
      };
      pdf
        .create(document, options)
        .then((res) => {
          response.sendFile("leaderboard.pdf", options2, function (err) {
            if (err) {
              return res.status(401).send(error);
            } else {
              fs.unlinkSync(options2.root + "leaderboard.pdf");
            }
          });
        })
        .catch((error) => {
          console.error(error);
        });
    }
  );
});
*/
app.get("/download-pdf/:type/:quid/:text", async (request, response) => {
  const { quid, type } = request.params;
  // var html = fs.readFileSync("public/templates/leaderboard.html", "utf8");
  var options = {
    format: "A4",
    orientation: "portrait",
    border: "0mm",
    // header: {
    //   height: "45mm",
    //   contents: '<div style="text-align: center;">Author: Shyam Hajare</div>',
    // },
    childProcessOptions: {
      env: {
        OPENSSL_CONF: "/dev/null",
      },
    },
  };
  // console.log("rankdaily",quid, type);

  // let data = null;
  const data = await connection.query(
    `call getPdfdata('${type}',${quid})`,
    async function (error, results) {
      if (error) console.log(error);
      console.log(results[0]);
      if (type === "questiondata") {
        const questions = [];
        results[1].forEach((element) => {
          questions.push({
            question: element.question,
            qNum: element.qNUm,
            options: element.options.split(",").map((op) => {
              op: op;
            }),
            correct: element.correct
              .split(",")
              .filter((e) => e.split("score")[1] == " 1]")
              ? element.correct
                  .split(",")
                  .filter((e) => e.split("score")[1] == " 1]")[0]
                  .split("score")[0]
                  .trim()
              : "",
          });
        });
        var document = {
          html: html,
          data: {
            questions: questions,
            header: results[0][0].quiz_name,
          },
          path: "public/leaderboard.pdf",
          type: "",
        };
        console.log(document.data);
      } else if (type === "rankdaily") {
        var html = fs.readFileSync("public/templates/rankdaily.html", "utf8");
        // console.log("rankdaily",results[1][0]);
        var document = {
          html: html,
          data: {
            users: results[1],
            toppers: results[1].slice(0, 3),
            name: results[0][0].name,
            title: results[0][0].title,
            // noOfExams: results[0][0].noOfExams,
            // totalAndMax: results[0][0].totalAndMax,
          },
          path: "public/rankdaily.pdf",
          type: "",
        };

        //   console.log(data)
        var options2 = {
          root: path.join(__dirname + "/public/"),
        };
        pdf
          .create(document, options)
          .then((res) => {
            response.sendFile("rankdaily.pdf", options2, function (err) {
              if (err) {
                return error;
              } else {
                fs.unlinkSync(options2.root + "rankdaily.pdf");
              }
            });
          })
          .catch((error) => {
            console.error(error);
          });
      } else {
        var html = fs.readFileSync("public/templates/leaderboard.html", "utf8");

        var document = {
          html: html,
          data: {
            users: results[1],
            toppers: results[1].slice(0, 3),
            header: results[0][0].name,
          },
          path: "public/leaderboard.pdf",
          type: "",
        };
      }
      //   console.log(data)
      var options2 = {
        root: path.join(__dirname + "/public/"),
      };
      pdf
        .create(document, options)
        .then((res) => {
          response.sendFile("leaderboard.pdf", options2, function (err) {
            if (err) {
              return error;
            } else {
              fs.unlinkSync(options2.root + "leaderboard.pdf");
            }
          });
        })
        .catch((error) => {
          console.error(error);
        });
    }
    // }
  );
});
app.post("/support", async (request, response) => {
  const { type, search, qid } = request.body;
  await connection.query(
    `CALL supportHome('${type}', '${search}', '${qid}')`,
    async function (err, resu) {
      if (err) response.send(err);
      response.send(resu);
    }
  );
});
app.get(
  "/get-user-payments-data/:type",
  verifyJwt,
  async (request, response) => {
    const { type } = request.params;
    const { phone } = request;

    await connection.query(
      `call spGetUserPaymentData('${type}','${phone}')`,
      async function (error, results) {
        if (error) response.send(error);
        response.send(JSON.stringify(results));
      }
    );
  }
);
app.get(
  "/download-payment-reciept/:gid/:num/:text",
  async (request, response) => {
    try {
      
      const { gid, num } = request.params;
      await connection.query(
        `call spGetUserPaymentData('${gid}','${num}')`,
        async function (error, results) {
          // connection.release();
          if (error) response.send(error);
          var html = fs.readFileSync("public/templates/payment.html", "utf8");
          var options = {
            format: "A4",
            orientation: "portrait",
            border: "0mm",
            // header: {
            //   height: "45mm",
            //   contents: '<div style="text-align: center;">Author: Shyam Hajare</div>',
            // },
            childProcessOptions: {
              env: {
                OPENSSL_CONF: "/dev/null",
              },
            },
          };
          if (results.length > 0) {
            var document = {
              html: html,
              data: {
                data: results[0][0],
              },
  
              path: "public/payment.pdf",
              type: "",
            };
            document.data.data.expiry_date =
              new Date(document.data.data.expiry_date).getDate() +
              " / " +
              new Date(document.data.data.expiry_date).getMonth() +
              " / " +
              new Date(document.data.data.expiry_date).getFullYear();
            var options2 = {
              root: path.join(__dirname + "/public/"),
            };
            pdf
              .create(document, options)
              .then((res) => {
                response.sendFile("payment.pdf", options2, function (err) {
                  if (err) {
                    return res.status(401).send(error);
                  } else {
                    fs.unlinkSync(options2.root + "payment.pdf");
                  }
                });
              })
              .catch((error) => {
                console.error(error);
              });
          } else {
            response.send(false);
          }
        }
      );
    } catch (error) {
      console.log(error,"download")
      
    }
  }
);
/*
app.post("/instamojo/pay", (req, res) => {
  Insta.setKeys(
    "402d6459443d9478470693d70670b362",
    "f7808a4e5cc85a23bcbfac39d7c817d1"
  );
  const data = new Insta.PaymentData();
  console.log(req.body);
  data.purpose = req.body.purpose;
  data.amount = req.body.amount;
  data.buyer_name = req.body.firstname;
  data.redirect_url = req.body.redirect_url;
  data.email = req.body.email;
  data.phone = req.body.phone;
  data.send_email = true;
  data.send_sms = true;
  data.allow_repeated_payments = false;
  Insta.createPayment(data, function (error, response) {
    if (error) {
      console.log(error);
      // some error
    } else {
      // Payment redirection link at response.payment_request.longurl
      try {
        const responseData = JSON.parse(response);
        console.log(responseData);
        const redirectUrl = responseData.payment_request?.longurl;
        console.log(redirectUrl);
        res.status(200).json(redirectUrl);
      } catch (error) {
        res.status(200).json("http://bit.ly/3QRVwg0");
      }
    }
  });
});
*/
app.post("/instamojo/pay", (req, res) => {
  Insta.setKeys(
    "402d6459443d9478470693d70670b362",
    "f7808a4e5cc85a23bcbfac39d7c817d1"
  );
  const data = new Insta.PaymentData();
  console.log(req.body);
  data.purpose = req.body.purpose;
  data.amount = req.body.amount;
  data.buyer_name = req.body.firstname;
  data.redirect_url = req.body.redirect_url;

  if (req.body.phone === "") {
    data.email = req.body.email;
    data.send_email = true;
  } else {
    data.email = "<EMAIL>";
    data.send_email = false;
  }
  data.phone = req.body.phone;

  data.send_sms = req.body.phone ? true : false;
  data.allow_repeated_payments = false;
  try {
    Insta.createPayment(data, function (error, response) {
      if (error) {
        console.log(error);
        // some error
      } else {
        // Payment redirection link at response.payment_request.longurl

        const responseData = JSON.parse(response);
        console.log(responseData);
        if (responseData.success) {
          const redirectUrl = responseData.payment_request?.longurl;
          console.log(redirectUrl);
          res.status(200).json(redirectUrl);
        } else {
          res.status(200).json("http://bit.ly/3QRVwg0");
        }
      }
    });
  } catch (error) {
    res.status(200).json("http://bit.ly/3QRVwg0");
  }
});
app.get("/payment_instamojo", async (req, res) => {
  console.log(req.query);
  if (req.query.payment_status === "Failed") {
    return res.redirect(
      `https://exams.navachaitanya.net/payment-failed?message="Payment Failed"`
    );
  } else {
    const {
      payment_status: status,
      firstname,
      net_amount_debit,
      user_id: phone,
      expiryDate,
      payment_request_id: txnid,
      fbw,
      gid,
    } = req.query;
    console.log(
      `CALL storePaymentDetails('${status}','${firstname}','${net_amount_debit}',${phone},'${txnid}','${new Date().toISOString()}','${gid}','${expiryDate}',${fbw})`
    );

    if (req.query?.type === "exam") {
      // insert into if no entry in paid_exams_users_mapping SELECT id, uid, quizid,price FROM nnekynbxen.paid_exams_users_mapping;

      connection.query(
        `INSERT INTO nnekynbxen.paid_exams_users_mapping (uid,quizid,price) 
        VALUES ('${phone}','${gid}','${net_amount_debit}')`,
        function (error, results, fields) {
          if (error) {
            console.log(error);
            return res.redirect(
              `https://exams.navachaitanya.net/payment-failed?message="Payment Failed"`
            );
          } else {
            return res.redirect(
              `https://exams.navachaitanya.net/exam-link/${gid}`
            );
          }
        }
      );
    }

    const title = `Payment received for amount Rs. ${net_amount_debit}`;
    const htmlScript = `<p><b>Paid Date: </b>${new Date().toISOString()}</p>
        <p><b>Transaction Id : </b>${txnid}</p>
        <p><b>Student Name : </b>${phone}</p>
        <p><b>Student Phone Number : </b>${firstname}</p>
        <br />
        <br />
        Team,
        NavaCHAITANYA Competitions LLP...
        `;
    // mailer(htmlScript, title);
    axios.post(
      `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
      {
        businessId: 1,
        verifyToken: Math.random() * 15000,
        phoneNumber: phone,
        message: [
          net_amount_debit,
          new Date().toDateString(),
          txnid,
          "https://exams.navachaitanya.net",
        ],
        messageType: "promotion",
        templateLang: "en",
        templateName: "payment_success",
      }
    );

    if (!req.query?.type) {
      // console.log(mailer(htmlScript, title))
      connection.query(
        `CALL storePaymentDetails('${status}','${firstname}','${net_amount_debit}',${phone},'${txnid}','${new Date().toISOString()}','${gid}','${expiryDate}',${fbw})`,

        async function (error, results) {
          if (results)
            return res.redirect(
              `https://exams.navachaitanya.net/?message="Your Package Activated Successfully..`
            );
        }
      );
    }

    connection.query(
      `CALL supportHome('getOnsignalId', '${phone}','0')`,
      async function (err, resu) {
        if (resu.length > 0) {
          let allSgnalIds = {};
          let count = 1;
          let users = 1;
          for (let each of resu[0]) {
            if (each.onesignalId) {
              if (!allSgnalIds[count]) {
                allSgnalIds[count] = []; // Initialize as an array if not already
              }
              allSgnalIds[count].push(each.onesignalId);
              users++;
            }
            if (users === 1999) {
              count++;
            }
          }

          const allUsers = Object.values(allSgnalIds);
          for (let each of allUsers) {
            await axios.post(`https://api.onesignal.com/notifications`, {
              app_id: "4f2fd23f-377c-40be-8cca-7867d0e6fdd6",
              name: "string",
              contents: {
                en: "Start practice now.",
              },
              headings: {
                en: "Your Package Activated Successfully..",
              },
              url: 'https://exams.navachaitanya.net/?message="Your Package Activated Successfully..',
              include_player_ids: each,
            });
          }
        }
      }
    );
  }
});
app.post("/cron/daily-report", async (req, res) => {
  await connection.query(
    `call getPaymentDataCsv('${dateSche}')`,
    async function (error, results) {
      console.log(error, results);
      if (results[0].length > 0 && results[0] != undefined) {
        const title = `AP SITE - Payment Report`;
        const htmlScript = `<p><b>Report Date: </b>${results[1][0].datefrom}</p>
        <p><b>No of Payments : </b>${results[1][0].total}</p>
        <p><b>Total Amount Recived : </b>${results[1][0].amount}</p>
        <br />
        <div>
          <a
            href="https://exams.navachaitanya.net/export-payment-data/${dateSche}"
            style="
              background-color: blue;
              color: white;
              padding: 10px;
              border-radius: 20px;
            "
            >Download Excel File</a
          >
        
        </div>
        `;
        mailer(htmlScript, title);
        // console.log(mailer(htmlScript, title))
      } else {
        const title = `AP SITE - Payment Report`;
        const htmlScript = `<p><b>Report Date: </b>${dateSche}</p>
        <p><b>No of Payments : </b>${0}</p>
        <p><b>Total Amount Recived : </b>0</p>
        <br />
        <p>No Payments are done </p>
        `;
        mailer(htmlScript, title);
      }

      res.send(true);
    }
  );
});

app.get("/download-questions/:type/:id", async (request, response) => {
  try {
    const { type, id } = request.params;
    const { phone } = request;

    await connection.query(
      `call spGetQuestionsData('${type}','${id}')`,
      async function (error, results) {
        if (results) {
          let data3 = {};
          results[0].forEach((each) => {
            if (data3[each.qid]) {
              data3[each.qid].push({
                question: each.question,
                option: each.text,
                score: each.score,
                question_in_english: each.question_in_english,
                option_in_english: each.option_in_english,
              });
            } else {
              data3[each.qid] = [
                {
                  question: each.question,

                  option: each.text,
                  score: each.score,
                  question_in_english: each.question_in_english,
                  option_in_english: each.option_in_english,
                },
              ];
            }
          });
          const dataArray = [];
          for (const eachQ of Object.keys(data3)) {
            let options = data3[eachQ];
            dataArray.push({
              question: options[0]?.question,
              option1: options[0]?.option,
              option1Correct: options[0]?.score,
              option2: options[1]?.option,
              option2Correct: options[1]?.score,
              option3: options[2]?.option,
              option3Correct: options[2]?.score,
              option4: options[3]?.option,
              option4Correct: options[3]?.score,
              questionInEnglish: options[0]?.question_in_english,
              option1InEnglish: options[0]?.option_in_english,
              option2InEnglish: options[1]?.option_in_english,
              option3InEnglish: options[2]?.option_in_english,
              option4InEnglish: options[3]?.option_in_english,
              correctAnswer:
                type === "englishExamQuestions"
                  ? options.find((e) => e.score === "correct")?.option
                  : "",
            });
          }

          // Process he results and generate the DOCX file
          const template =
            type === "englishExamQuestions"
              ? `<div>
    <table style="border-collapse: collapse; width: 100%; border: 1px solid black;">
        <tr>
            <td style="border: 1px solid black; padding: 8px;">Question</td>
            <td style="border: 1px solid black; padding: 8px;">{question}</td>
            <td style="border: 1px solid black; padding: 8px;">{questionInEnglish}</td>
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 8px;">Type</td>
            <td colspan="2" style="border: 1px solid black; padding: 8px;">multiple choice</td>
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 8px;">Option</td>
            <td style="border: 1px solid black; padding: 8px;">{option1}</td>
            <td style="border: 1px solid black; padding: 8px;">{option1InEnglish}</td>
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 8px;">Option</td>
            <td style="border: 1px solid black; padding: 8px;">{option2}</td>
            <td style="border: 1px solid black; padding: 8px;">{option2InEnglish}</td>
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 8px;">Option</td>
            <td style="border: 1px solid black; padding: 8px;">{option3}</td>
            <td style="border: 1px solid black; padding: 8px;">{option3InEnglish}</td>
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 8px;">Option</td>
            <td style="border: 1px solid black; padding: 8px;">{option4}</td>
            <td style="border: 1px solid black; padding: 8px;">{option4InEnglish}</td>
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 8px;">Correct Answer</td>
            <td colspan="2" style="border: 1px solid black; padding: 8px;">{correctAnswer}</td>
        </tr>
    </table>
</div>
`
              : `
                <div>
               
      
<table style="border-collapse: collapse; margin-bottom: 15px;page-break-before: always;width: 100%;">
    <tr>
        <td style="border: 1px solid black;">Question</td>
        <td colspan="2" style="border: 1px solid black;">{question}</td>
    </tr>
    <tr>
        <td style="border: 1px solid black;">Type</td>
        <td colspan="2" style="border: 1px solid black;">multiple_choice</td>
    </tr>
    <tr>
        <td style="border: 1px solid black;">Option</td>
        <td style="border: 1px solid black;">{option1}</td>
        <td style="border: 1px solid black;">{option1Correct}</td>
    </tr>
    <tr>
        <td style="border: 1px solid black;">Option</td>
        <td style="border: 1px solid black;">{option2}</td>
        <td style="border: 1px solid black;">{option2Correct}</td>
    </tr>
    <tr>
        <td style="border: 1px solid black;">Option</td>
        <td style="border: 1px solid black;">{option3}</td>
        <td style="border: 1px solid black;">{option3Correct}</td>
    </tr>
    <tr>
        <td style="border: 1px solid black;">Option</td>
        <td style="border: 1px solid black;">{option4}</td>
        <td style="border: 1px solid black;">{option4Correct}</td>
    </tr>
    <tr>
        <td style="border: 1px solid black;">Solution</td>
        <td colspan="2" style="border: 1px solid black;"></td>
    </tr>
    <tr>
        <td style="border: 1px solid black;">Marks</td>
        <td  style="border: 1px solid black;">1</td>
       <td style="border: 1px solid black;">0</td>

    </tr>
</table>
 </div>
`;
          let generatedHtml = "";
          dataArray.forEach((data) => {
            const htmlRow = template
              .replace(/{question}/g, data.question)
              .replace(/{option1}/g, data.option1)
              .replace(/{option1Correct}/g, data.option1Correct)
              .replace(/{option2}/g, data.option2)
              .replace(/{option2Correct}/g, data.option2Correct)
              .replace(/{option3}/g, data.option3)
              .replace(/{option3Correct}/g, data.option3Correct)
              .replace(/{option4}/g, data.option4)
              .replace(/{option4Correct}/g, data.option4Correct)
              .replace(/{questionInEnglish}/g, data.questionInEnglish)
              .replace(/{option1InEnglish}/g, data.option1InEnglish)
              .replace(/{option2InEnglish}/g, data.option2InEnglish)
              .replace(/{option3InEnglish}/g, data.option3InEnglish)
              .replace(/{option4InEnglish}/g, data.option4InEnglish)
              .replace(/{correctAnswer}/g, data.correctAnswer);

            generatedHtml += htmlRow + "<br/>";
          });
          // const generatedHtmlPages = dataArray.map(data => {
          //     return template
          //         .replace(/{question}/g, data.question)
          //         .replace(/{option1}/g, data.option1)
          //         .replace(/{option1Correct}/g, data.option1Correct)
          //         .replace(/{option2}/g, data.option2)
          //         .replace(/{option2Correct}/g, data.option2Correct)
          //         .replace(/{option3}/g, data.option3)
          //         .replace(/{option3Correct}/g, data.option3Correct)
          //         .replace(/{option4}/g, data.option4)
          //         .replace(/{option4Correct}/g, data.option4Correct);
          // });
          const docxBuffer = htmlToDocx.asBlob(generatedHtml);
          const bufferData = Buffer.from(await docxBuffer.arrayBuffer());

          const generatedFilePath = path.join(
            __dirname,
            "public",
            "questions_export.docx"
          );
          fs.writeFileSync(generatedFilePath, bufferData);
          // Send the generated file to the user
          response.download(
            generatedFilePath,
            "questions_export.docx",
            (err) => {
              if (err) {
                console.error("Error sending file:", err);
              }

              // Remove the generated file after sending
              fs.unlinkSync(generatedFilePath);
            }
          );
          // const generatedFilePath = path.join(__dirname, 'public', 'questions_export.docx');

          // const docxBuffers = await Promise.all(generatedHtmlPages.map(async htmlPage => {
          //     const docxBuffer = await htmlToDocx.asBlob(htmlPage);
          //     return Buffer.from(await docxBuffer.arrayBuffer());
          // }));

          // // Combine buffers into a single DOCX file
          // const combinedBuffer = Buffer.concat(docxBuffers);

          // fs.writeFileSync(generatedFilePath, combinedBuffer);

          // // Send the generated file to the user
          // response.download(generatedFilePath, 'questions_export.docx', (err) => {
          //     if (err) {
          //         console.error('Error sending file:', err);
          //     }

          //     // Remove the generated file after sending
          //     fs.unlinkSync(generatedFilePath);
          // });
        }
      }
    );
  } catch (error) {
    response.status(500).send(error.message);
  }
});

app.get("/api-webhook", (request, response) => {
  const VERIFY_TOKEN = "ncexams";
  let mode = request.query["hub.mode"];
  let token = request.query["hub.verify_token"];
  let challenge = request.query["hub.challenge"];

  if (mode && token && mode === "subscribe" && token === VERIFY_TOKEN) {
    console.log("WEBHOOK_VERIFIED");
    return response.status(200).send(challenge);
  }
  return response.sendStatus(403);
});

app.post("/api-webhook", async (request, response) => {
  try {
    if (request.body.entry[0].changes[0].value.messages) {
      let number = request.body.entry[0].changes[0].value.messages[0].from;
      let userName =
        request.body.entry[0].changes[0].value.contacts[0].profile.name;
      const msgId = request.body.entry[0].changes[0].value.messages[0].id;

      if (
        request.body.entry[0].changes[0].value.messages[0].type !==
        "interactive"
      ) {
        // number = number.length > 10 ? number.slice(2) : number;
        let messageText =
          request.body.entry[0].changes[0].value.messages[0].text.body;
        const quyery = `CALL supportHome('getMessageReply', ${JSON.stringify(
          messageText
        )}, '')`;

        storeINExcel(userName, number, messageText);

        await connection.query(quyery, async function (err, resu) {
          if (err) response.send(err);
          // response.send(resu);
          let messageToReply = resu[0][0].messageToReply;
          if (messageToReply) {
            await axios.post(
              `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
              {
                businessId: 1,
                verifyToken: Math.random() * 15000,
                phoneNumber: number,
                message: messageToReply,
                messageType: "simple",
                templateLang: "en",
                templateName: "message_recieved",
              }
            );
          }
        });
        const title = `You got new message from student( ${number} - ${userName} )\n\n*Message* - ${messageText} `;
        await axios.post(
          `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
          {
            businessId: 1,
            verifyToken: Math.random() * 15000,
            phoneNumber: "919014298950",
            message: [
              `${number} - ${userName}`,
              `${messageText}`,
              `https://api.whatsapp.com/send?phone=${number}&amp;text=Hai`,
            ],
            messageType: "promotion",
            templateLang: "en",
            templateName: "message_recieved",
          }
        );
        const htmlScript = `
        <p style="font-size: 16px;">Dear Admin,</p>
        <p style="font-size: 16px;">You got a new message from student ( ${number} - ${userName} )</p>
        <p style="font-size: 16px;"><strong>Message:</strong> ${messageText}</p>
        <p style="font-size: 16px;">To reply, click on the button below:</p>
        <a href="https://exams.navachaitanya.net/support/user/${
          number.length > 10 ? number.slice(2) : number
        }" style="display: inline-block; padding: 10px 20px; font-size: 16px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">Reply</a>
        <p style="font-size: 16px;">or</p>
        <p style="font-size: 16px;">Send a message on WhatsApp to the student:</p>
        <a href="https://api.whatsapp.com/send?phone=${number}&amp;text=Hai" style="display: inline-block; padding: 10px 20px; font-size: 16px; background-color: #25D366; color: white; text-decoration: none; border-radius: 5px;">WhatsApp</a>
    `;
        // mailer(htmlScript, title);
      }
      response.sendStatus(201);
    }
  } catch (err) {}
});
app.post("/send-verification-msg", async (request, res) => {
  const { type, phone, otp } = request.body;
  console.log("request", request.headers.origin);
  await connection.query(
    `call spSendVerifySms('${type}','${phone}','${otp}')`,
    async function (error, results) {
      if (error) res.send(error);
      console.log(results[0][0]);
      if (results[0][0].result === "success") {
        try {
          if (type == "send") {
            // await sms.sendSms(results[0][0].message, phone);
            try {
              // axios.get(
              //   `http://voice.roundsms.co/api/sendmsg.php?user=nookesh01&pass=123456&sender=NCEXAM&phone=${phone}&text=${results[0][0].message}&priority=normal&stype=ndnd`
              // );
              // const generateToken = await axios.post(
              //   "https://bulksms.bsnl.in:5010/api/Create_New_API_Token",
              //   {
              //     Service_Id: "10656",
              //     Username: "Chaitu7174",
              //     Password: "Myacer@9999",
              //     Token_Id: "1",
              //     IP_Addresses: [""],
              //   },
              //   {
              //     headers: {
              //       "content-type": "application/json; charset=utf-8",
              //     },
              //   }
              // );
              // axios.post(
              //   "https://bulksms.bsnl.in:5010/api/Send_SMS",
              //   {
              //     Consent_Template_Id: "",
              //     Content_Template_Id: "1407170615675518090",
              //     Entity_Id: "1401690280000065252",
              //     Header: "NCEXAM",
              //     Is_Flash: "0",
              //     Is_Unicode: "0",
              //     Message_Type: "SI",
              //     Target: phone,
              //     Template_Keys_and_Values: [
              //       {
              //         Key: "var",
              //         Value: results[0][0].message,
              //       },
              //     ],
              //   },
              //   {
              //     headers: {
              //       "content-type": "application/json; charset=utf-8",
              //       Authorization: 'Bearer ApPOkoXEeg3nChH_DXXGK5NbuvTop35TwAgi6PAspVU',
              //     },
              //   }
              // );

              // Define the data payload for the POST request
              // const postData = {
              //   Consent_Template_Id: "",
              //   Content_Template_Id: "1407170615675518090",
              //   Entity_Id: "1401690280000065252",
              //   Header: "NCEXAM",
              //   Is_Flash: "0",
              //   Is_Unicode: "0",
              //   Message_Type: "SI",
              //   Target: phone, // Make sure 'phone' variable is defined and contains the recipient's phone number
              //   Template_Keys_and_Values: [
              //     {
              //       Key: "var",
              //       Value: results[0][0].message, // Make sure 'results' variable is defined and contains the message content
              //     },
              //   ],
              // };
              // const generateToken = await axios.post(
              //   "https://bulksms.bsnl.in:5010/api/Create_New_API_Token",
              //   {
              //     Service_Id: "10656",
              //     Username: "Chaitu7174",
              //     Password: "Myacer@9999",
              //     Token_Id: "2",
              //     IP_Addresses: [""],
              //   },
              //   {
              //     headers: {
              //       "content-type": "application/json; charset=utf-8",
              //     },
              //   }
              // );
              const sendMsg = (phone_new) => {
                axios.post(
                  `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
                  {
                    businessId: 1,
                    verifyToken: Math.random() * 15000,
                    phoneNumber: phone_new,
                    message: [results[0][0].message],
                    messageType: "promotion",
                    templateLang: "en",
                    templateName: "new_msg_status",
                  }
                );
              };
              sendMsg(phone);
              const postData = {
                route: "dlt",
                sender_id: "NCEXAM",
                message: "174972",
                variables_values: results[0][0].message + "|",
                flash: 0,
                numbers: phone,
              };
              let config = {
                method: "POST",
                maxBodyLength: Infinity,
                url: "https://www.fast2sms.com/dev/bulkV2",
                headers: {
                  authorization:
                    "WbVCQdMNqGES4JY0T8Bxc2DlaOpHUKt6gweXm1RLzPi3FhZvsnIOjKteJCUVPZnHGoMWql5d610v8YNr",
                  "Content-Type": "application/json",
                },
                data: postData,
              };
              console.log("config", config);
              axios
                .request(config)
                .then((response) => {
                  console.log(JSON.stringify(response.data));
                })
                .catch((error) => {
                  console.log("error", error);
                });

              //   // Define the request headers, including content type and authorization
              // const headers = {
              //   "content-type": "application/json; charset=utf-8",
              //   Authorization:
              //     "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1bmlxdWVfbmFtZSI6IjEwNjU2IDMiLCJuYmYiOjE3Mjc5MzYwMTUsImV4cCI6MTc1OTQ3MjAxNSwiaWF0IjoxNzI3OTM2MDE1LCJpc3MiOiJodHRwczovL2J1bGtzbXMuYnNubC5pbjo1MDEwIiwiYXVkIjoiMTA2NTYgMyJ9.fSrCmpLGk-2PT4XvFLQ2--kH7FpiOjbov9EDXMP5UzQ",
              // };
              // // Make the POST request using axios
              // axios
              //   .post("https://bulksms.bsnl.in:5010/api/Send_SMS", postData, {
              //     headers,
              //   })
              //   .then((response) => {
              //     console.log("Response:", response.data); // Handle response as needed
              //   })
              //   .catch((error) => {
              //     console.error("Error:"); // Handle error if request fails
              //   });
              // let config = {
              //   method: "POST",
              //   maxBodyLength: Infinity,
              //   url: "https://bulksms.bsnl.in:5010/api/Send_SMS_Bulk_Individual",
              //   headers: {
              //     "Content-Type": "application/json; charset=utf-8",
              //     Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1bmlxdWVfbmFtZSI6IjEwNjU2IDMiLCJuYmYiOjE3Mjc5MzYwMTUsImV4cCI6MTc1OTQ3MjAxNSwiaWF0IjoxNzI3OTM2MDE1LCJpc3MiOiJodHRwczovL2J1bGtzbXMuYnNubC5pbjo1MDEwIiwiYXVkIjoiMTA2NTYgMyJ9.fSrCmpLGk-2PT4XvFLQ2--kH7FpiOjbov9EDXMP5UzQ" ,
              //   },
              //   data: JSON.stringify(postData),
              // };

              // axios
              //   .request(config)
              //   .then((response) => {
              //     console.log(JSON.stringify(response.data));
              //   })
              //   .catch((error) => {
              //     console.log("error");
              //   });
            } catch (error) {
              console.log("error", error);
            }
          }
          res.sendStatus(201);
        } catch (error) {}
      } else {
        res.sendStatus(400);
      }
    }
  );
});

function generateTransactionID() {
  const timestamp = Date.now();
  const randomNum = Math.floor(Math.random() * 1000000);
  const merchantPrefix = "T";
  const transactionID = `${merchantPrefix}${timestamp}${randomNum}`;
  return transactionID;
}

// payment route
app.post("/phonepe/payment", async (req, res) => {
  try {
    const { firstname, phone, amount, redirect_url } = req.body;
    const payload = {
      merchantId: "M22TVSLFNFUG7",
      merchantTransactionId: generateTransactionID(),
      merchantUserId: "M22TVSLFNFUG7",
      name: firstname,
      amount: amount * 100,
      redirectUrl: redirect_url,
      redirectMode: "POST",
      mobileNumber: phone,
      paymentInstrument: {
        type: "PAY_PAGE",
      },
    };

    const payloadMain = Buffer.from(JSON.stringify(payload)).toString("base64");
    const key = "549a4a38-5cc5-4192-9918-38e8d23bb803";
    const keyIndex = 1;
    const string = payloadMain + "/pg/v1/pay" + key;
    const sha256 = crypto.createHash("sha256").update(string).digest("hex");
    const checksum = sha256 + "###" + keyIndex;

    // const URL = "https://api-preprod.phonepe.com/apis/pg-sandbox/pg/v1/pay";
    const URL = "https://api.phonepe.com/apis/hermes/pg/v1/pay";
    const options = {
      method: "POST",
      url: URL,
      headers: {
        accept: "application/json",
        "Content-Type": "application/json",
        "X-VERIFY": checksum,
      },
      data: {
        request: payloadMain,
      },
    };

    axios
      .request(options)
      .then(function (response) {
        return res
          .status(200)
          .send(response.data.data.instrumentResponse.redirectInfo.url);
      })
      .catch(function (error) {
        console.error(error);
      });
  } catch (err) {}
});
// payment status
app.post("/phonepe/status", async (req, res) => {
  // return console.log(res.req.body);
  const merchantTransactionId = req.body.transactionId;
  const merchantId = req.body.merchantId;
  const keyIndex = 1;
  const key = "549a4a38-5cc5-4192-9918-38e8d23bb803";
  const string = `/pg/v1/status/${merchantId}/${merchantTransactionId}` + key;
  const sha256 = crypto.createHash("sha256").update(string).digest("hex");
  const checksum = sha256 + "###" + keyIndex;

  // const URL = `https://api-preprod.phonepe.com/apis/pg-sandbox/pg/v1/status/${merchantId}/${merchantTransactionId}`;
  const URL = `https://api.phonepe.com/apis/hermes/pg/v1/status/M22TVSLFNFUG7/${merchantTransactionId}`;
  const options = {
    method: "GET",
    url: URL,
    headers: {
      accept: "application/json",
      "Content-Type": "application/json",
      "X-VERIFY": checksum,
      "X-MERCHANT-ID": merchantId,
    },
  };

  // CHECK PAYMENT STATUS
  axios
    .request(options)
    .then(async (response) => {
      if (response.data.success && response.data.code === "PAYMENT_SUCCESS") {
        const {
          firstname,
          net_amount_debit,
          user_id: phone,
          expiryDate,
          payment_request_id: txnid,
          fbw,
          gid,
        } = req.query;
        const status = "Paid";
        console.log(
          `CALL storePaymentDetails('${status}','${firstname}','${net_amount_debit}',${phone},'${txnid}','${new Date().toISOString()}','${gid}','${expiryDate}',${fbw})`
        );
        const title = `Payment received for amount Rs. ${net_amount_debit}`;
        const htmlScript = `<p><b>Paid Date: </b>${new Date().toISOString()}</p>
            <p><b>Transaction Id : </b>${txnid}</p>
            <p><b>Student Name : </b>${phone}</p>
            <p><b>Student Phone Number : </b>${firstname}</p>
            <br />
            <br />
            Team,
            NavaCHAITANYA Competitions LLP...
            `;
        // mailer(htmlScript, title);
        axios.post(
          `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
          {
            businessId: 1,
            verifyToken: Math.random() * 15000,
            phoneNumber: phone,
            message: [
              net_amount_debit,
              new Date().toDateString(),
              txnid,
              "https://exams.navachaitanya.net",
            ],
            messageType: "promotion",
            templateLang: "en",
            templateName: "payment_success",
          }
        );

        if (!req.query?.type) {
          // console.log(mailer(htmlScript, title))
          connection.query(
            `CALL storePaymentDetails('${status}','${firstname}','${net_amount_debit}',${phone},'${txnid}','${new Date().toISOString()}','${gid}','${expiryDate}',${fbw})`,

            async function (error, results) {
              if (results) {
                const group = results[0][0].groupName;

                const whatsapp_msg_sent = results[0][0].whatsapp_msg_sent;
                const pid = results[0][0].pid;

                if (whatsapp_msg_sent === "0") {
                  const postData = {
                    businessId: 1,
                    verifyToken: Math.random() * 15000,
                    phoneNumber: 9441687174,
                    message: [
                      net_amount_debit,
                      firstname + " (" + phone + ")",
                      new Date().toDateString(),
                      gid + " (" + group + ")",
                      pid,
                    ],
                    messageType: "promotion",
                    templateLang: "en",
                    templateName: "exams_order_admin_status",
                  };
                  fetch(
                    "https://phpstack-702151-4218790.cloudwaysapps.com/send-message",
                    {
                      method: "POST",
                      headers: {
                        "Content-Type": "application/json",
                      },
                      body: JSON.stringify(postData),
                    }
                  )
                    .then((response) => response.json())
                    .then((data) => {})
                    .catch((error) => {
                      console.error("Error:", error);
                    });
                  return res.redirect(
                    `https://exams.navachaitanya.net/?message="Your Package Activated Successfully..`
                  );
                }
              }
            }
          );
        }

        if (req.query?.type === "exam") {
          // insert into if no entry in paid_exams_users_mapping SELECT id, uid, quizid,price FROM nnekynbxen.paid_exams_users_mapping;

          connection.query(
            `INSERT INTO nnekynbxen.paid_exams_users_mapping (uid,quizid,price) 
        VALUES ('${phone}','${gid}','${net_amount_debit}')`,
            function (error, results, fields) {
              if (error) {
                console.log(error);
                return res.redirect(
                  `https://exams.navachaitanya.net/payment-failed?message="Payment Failed"`
                );
              } else {
                return res.redirect(
                  `https://exams.navachaitanya.net/exam-link/${gid}`
                );
              }
            }
          );
        }

        connection.query(
          `CALL supportHome('getOnsignalId', '${phone}','0')`,
          async function (err, resu) {
            if (resu.length > 0) {
              let allSgnalIds = {};
              let count = 1;
              let users = 1;
              for (let each of resu[0]) {
                if (each.onesignalId) {
                  if (!allSgnalIds[count]) {
                    allSgnalIds[count] = []; // Initialize as an array if not already
                  }
                  allSgnalIds[count].push(each.onesignalId);
                  users++;
                }
                if (users === 1999) {
                  count++;
                }
              }

              const allUsers = Object.values(allSgnalIds);
              for (let each of allUsers) {
                await axios.post(`https://api.onesignal.com/notifications`, {
                  app_id: "4f2fd23f-377c-40be-8cca-7867d0e6fdd6",
                  name: "string",
                  contents: {
                    en: "Start practice now.",
                  },
                  headings: {
                    en: "Your Package Activated Successfully..",
                  },
                  url: 'https://exams.navachaitanya.net/?message="Your Package Activated Successfully..',
                  include_player_ids: each,
                });
              }
            }
          }
        );
      } else {
        return res.redirect(
          `https://exams.navachaitanya.net/payment-failed?message="Payment Failed"`
        );
      }
    })
    .catch((error) => {
      return res.redirect(
        `https://exams.navachaitanya.net/payment-failed?message="Payment Failed"`
      );
    });
});

app.post("/send-bulk-sms", async (request, res) => {
  const {
    message,
    templateId,
    Is_Unicode = "0",
    phone,
    header,
    Entity_Id,
    Message_Type,
    Template_Keys = "var",
  } = request.body;

  try {
    const generateToken = await axios.post(
      "https://bulksms.bsnl.in:5010/api/Create_New_API_Token",
      {
        Service_Id: "10656",
        Username: "Chaitu7174",
        Password: "Myacer@9999",
        Token_Id: "1",
        IP_Addresses: [""],
      },
      {
        headers: {
          "content-type": "application/json; charset=utf-8",
        },
      }
    );
    const targetMessages = phone?.map((e) => e?.contact_no);

    const messageChunks = chunkArray(targetMessages, 400);
    for (const chunk of messageChunks) {
      const postData = {
        Consent_Template_Id: "",
        Content_Template_Id: templateId,
        Entity_Id: Entity_Id,
        Header: header,
        Is_Flash: "0",
        Is_Unicode: Is_Unicode,
        Message_Type: Message_Type,
        Targets: chunk,
        Template_Keys_and_Values: [
          {
            Key: "var",
            Value: Template_Keys ? message : "",
          },
        ],
      };

      let config = {
        method: "POST",
        maxBodyLength: Infinity,
        url: "https://bulksms.bsnl.in:5010/api/Send_SMS_Bulk",
        headers: {
          "Content-Type": "application/json; charset=utf-8",
          Authorization: "Bearer " + generateToken?.data,
        },
        data: JSON.stringify(postData),
      };

      try {
        const response = await axios.request(config);
        console.log("🚀 ~ app.post ~ response:", response.data);
      } catch (error) {
        console.error("Error sending message chunk:", error);
        res.send(false);
        return;
      }
    }
    res.send(true);
  } catch (error) {
    res.send(false);
  }
});
const chunkArray = (arr, size) => {
  let chunks = [];
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  return chunks;
};

app.get("/sms-templates", async (request, res) => {
  try {
    const postData = {};
    const generateToken = await axios.post(
      "https://bulksms.bsnl.in:5010/api/Create_New_API_Token",
      {
        Service_Id: "10656",
        Username: "Chaitu7174",
        Password: "Myacer@9999",
        Token_Id: "2",
        IP_Addresses: [""],
      },
      {
        headers: {
          "content-type": "application/json; charset=utf-8",
        },
      }
    );
    let config = {
      method: "POST",
      maxBodyLength: Infinity,
      url: "https://bulksms.bsnl.in:5010/api/Get_Content_Template_Details",
      headers: {
        "Content-Type": "application/json; charset=utf-8",
        Authorization: "Bearer " + generateToken?.data,
      },
      // data: JSON.stringify(postData),
    };

    axios
      .request(config)
      .then((response) => {
        res.send({ result: true, data: response.data });
      })
      .catch((error) => {
        res.send({ result: false, data: error });

        console.log("error");
      });
  } catch (error) {}
});

app.get("/sms-count", async (request, res) => {
  try {
    const generateToken = await axios.post(
      "https://bulksms.bsnl.in:5010/api/Create_New_API_Token",
      {
        Service_Id: "10656",
        Username: "Chaitu7174",
        Password: "Myacer@9999",
        Token_Id: "1",
        IP_Addresses: [""],
      },
      {
        headers: {
          "content-type": "application/json; charset=utf-8",
        },
      }
    );
    var options = {
      method: "POST",
      url: "https://bulksms.bsnl.in:5010/api/Get_SMS_Count",
      headers: {
        Accept: "*/*",
        "User-Agent": "Thunder Client (https://www.thunderclient.com)",
        Authorization: "Bearer " + generateToken.data,
      },
    };

    axios
      .request(options)
      .then(function (response) {
        res.send({ result: true, data: response.data });
      })
      .catch(function (error) {
        res.send({ result: false, data: error });
      });
  } catch (error) {}
});

// create an api to call openai batch processing api to get the result in the same format i sent to gpt-4o-mini,i want to do translation of data from telugu to english language add system prompt also ,i will give json data in telugu language,i need response in the same format in english language

app.post("/translate", async (req, res) => {
  const { key } = req.body;
  if (key === "ncexams") {
    // const openai = new OpenAI({
    //   apiKey:
    //     "********************************************************************************************************************************************************************",
    // });
    try {
      // Fetch 5 questions and their options from the database
      await connection.query(
        `
      SELECT q.qid, q.question, 
      JSON_ARRAYAGG(JSON_OBJECT('oid', o.oid, 'q_option', o.q_option)) AS options
      FROM savsoft_qbank q 
      JOIN (SELECT cid FROM savsoft_category
      WHERE translate_ai='1'
      order by rand())c ON q.cid = c.cid
      JOIN savsoft_options o ON q.qid = o.qid 
      WHERE q.question_in_english IS NULL AND question not like '%data:image%'
      GROUP BY q.qid, q.question
      order by q.qid desc
      LIMIT 1
    `,
        async (err, questions) => {
          if (err) {
            console.log(err);
          } else {
            if (questions.length === 0) {
              return res.json({ message: "No questions to translate" });
            }

            // Prepare data for OpenAI request
            const data = questions.map((q) => ({
              question: q.question,
              options: JSON.parse(q.options).map((opt) => opt.q_option),
            }));
            res.json({ message: "question found.it will be queue" });

            const response = await axios.post(
              "https://api.openai.com/v1/chat/completions",
              {
                model: "gpt-4o-mini",
                messages: [
                  {
                    role: "system",
                    content:
                      "Translate the provided JSON data from Telugu to English, preserving the original JSON structure. Do not use code block formatting like ```json```. Provide the translated JSON array directly.",
                  },
                  {
                    role: "user",
                    content: JSON.stringify(data),
                  },
                ],
                temperature: 0,
                top_p: 1,
                frequency_penalty: 0,
                presence_penalty: 0,
              },
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization:
                    "Bearer ********************************************************************************************************************************************************************",
                },
              }
            );

            const translatedData = JSON.parse(
              response.data.choices[0].message.content
            );

            try {
              // Update the translated questions and options back into the database
              // for (let i = 0; i < questions.length; i++) {

              // // Generate a single query to update all questions and options
              // let updateQuery = `UPDATE savsoft_qbank q
              // JOIN savsoft_options o ON q.qid = o.qid
              // SET q.question_in_english = CASE q.qid `;

              // questions.forEach((q, index) => {
              //   if (translatedData[index].question) {
              //   updateQuery += `WHEN ${q.qid} THEN "${translatedData[index].question}" `;
              //   }
              // });

              // updateQuery += `END, o.option_in_english = CASE o.oid `;

              // questions.forEach((q, index) => {
              //   JSON.parse(q.options).forEach((opt, optIndex) => {
              //   if (translatedData[index].options[optIndex]) {
              //     updateQuery += `WHEN ${opt.oid} THEN "${translatedData[index].options[optIndex]}" `;
              //   }
              //   });
              // });

              // updateQuery += `END WHERE q.qid IN (${questions.map(q => q.qid).join(", ")})`;

              // await connection.query(updateQuery, (err, result) => {
              //   if (err) {
              //   console.error("Error updating data:", err);
              //   }
              // });
              // }

              for (let i = 0; i < questions.length; i++) {
                // Generate a single query to update all questions and options
                let updateQuery = `
                  UPDATE savsoft_qbank q
                  JOIN savsoft_options o ON q.qid = o.qid
                  SET q.question_in_english = CASE q.qid `;

                questions.forEach((q, index) => {
                  if (translatedData[index].question) {
                    updateQuery += `WHEN ${q.qid} THEN '${translatedData[
                      index
                    ].question.replace(/'/g, "''")}' `;
                  }
                });

                updateQuery += `END, o.option_in_english = CASE o.oid `;

                questions.forEach((q, index) => {
                  JSON.parse(q.options).forEach((opt, optIndex) => {
                    if (translatedData[index].options[optIndex]) {
                      updateQuery += `WHEN ${opt.oid} THEN '${translatedData[
                        index
                      ].options[optIndex].replace(/'/g, "''")}' `;
                    }
                  });
                });

                updateQuery += `END WHERE q.qid IN (${questions
                  .map((q) => q.qid)
                  .join(", ")})`;

                await connection.query(updateQuery, (err, result) => {
                  if (err) {
                    console.error("Error updating data:", err);
                  }
                });
              }
            } catch (error) {}
          }
        }
      );
    } catch (error) {
      console.error("Error translating data:", error);
      res.status(500).json({ error: "Internal Server Error" });
    }
  } else {
    res.status(401).json({ error: "Unauthorized" });
  }
});

app.post(
  "/moderator/add-edit-question",
  verifyJwt,
  async (request, response) => {
    // check moderator role or not in savsoft users table
    const { uid } = request.body;
    await connection.query(
      `SELECT su FROM savsoft_users WHERE uid = ${uid}`,
      async function (err, resu) {
        if (err) {
          response
            .status(500)
            .json({ error: "Internal Server Error", message: err });
          return;
        }
        if (resu[0].su !== "2") {
          response.status(401).json({ error: "Unauthorized" });
          return;
        }
        if (resu[0].su === "2") {
          let {
            questionText,
            op1,
            op2,
            op3,
            op4,
            op1Num,
            op2Num,
            op3Num,
            op4Num,
            selectedCat,
            correctOption,
            qNum,
            quizId,
            type,
            op5,
            op5Num,
            explanation,
            isEnglishMedium,
          } = request.body;
          questionText = questionText.replace("'s", "`s");
          questionText = questionText.replace("�s", "`s");
          op1 = op1.replace("'s", "`s");
          op1 = op1.replace("�s", "`s");
          op2 = op2.replace("'s", "`s");
          op2 = op2.replace("�s", "`s");
          op3 = op3.replace("'s", "`s");
          op3 = op3.replace("�s", "`s");
          op4 = op4.replace("'s", "`s");
          op4 = op4.replace("�s", "`s");
          op5 = op5.replace("'s", "`s");
          op5 = op5.replace("�s", "`s");
          await connection.query(
            `CALL ModeratorAddEditQuestion('${type}','${questionText}', '${op1}', '${op2}', '${op3}','${op4}', '${selectedCat}','${correctOption}',${qNum},'${quizId}',${op1Num},${op2Num},${op3Num},${op4Num},'${op5}',${op5Num},'${explanation}',${isEnglishMedium})`,
            async function (err, resu) {
              if (err) {
                response.send(err);
              } else {
                response.send(resu);
              }
            }
          );
        }
      }
    );
  }
);

app.post(
  "/moderator/add-edit-exam",
  verifyJwt,

  async (req, res) => {
    try {
      const { uid, type, examId, examName, description, startDate } = req.body;

      if (!uid || !type || !examName) {
        return res.status(400).json({ error: "Missing required fields" });
      }

      await connection.query(
        `SELECT su FROM savsoft_users WHERE contact_no = '${uid}'`,
        async function (err, resu) {
          if (err) {
            res
              .status(500)
              .json({ error: "Internal Server Error", message: err });
            return;
          }
          if (resu[0].su !== "2") {
            res.status(401).json({ error: "Unauthorized" });
            return;
          }
          if (resu[0].su === "2") {
            await connection.query(
              `CALL ModeratorAddEditExams('${type}', ${
                examId || 0
              }, '${examName}', '${
                description || ""
              }', '${uid}', '${startDate}')`,
              async function (err, resu) {
                if (err) {
                  res
                    .status(500)
                    .json({ error: "Database error", message: err });
                } else {
                  res.send(resu);
                }
              }
            );
            res.status(200).json({ message: "Authorized" });
          }
        }
      );
    } catch (error) {
      console.error("Database error:", error);
      res
        .status(500)
        .json({ error: "Internal Server Error", message: error.message });
    }
  }
);

app.post("/moderator/add-questions-to-exam", verifyJwt, async (req, res) => {
  try {
    const { uid, examId, qids } = req.body;

    if (!uid || !examId) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    await connection.query(
      `SELECT su FROM savsoft_users WHERE contact_no = ${uid}`,
      async function (err, resu) {
        if (err) {
          res
            .status(500)
            .json({ error: "Internal Server Error", message: err });
          return;
        }
        if (resu[0].su !== "2") {
          res.status(401).json({ error: "Unauthorized" });
          return;
        }
        if (resu[0].su === "2") {
          await connection.query(
            `CALL AddQuestionsToExam(${examId}, '${qids}')`,
            async function (err, resu) {
              if (err) {
                res.send(err);
              } else {
                res.send(resu);
              }
            }
          );
        }
      }
    );
  } catch (error) {
    console.error("Database error:", error);
    res
      .status(500)
      .json({ error: "Internal Server Error", message: error.message });
  }
});

app.get("/moderator/get-exams", verifyJwt, async (req, res) => {
  const { phone } = req;
  const { page } = req.query;
  try {
    // convert UNIX_TIMESTAMP to date string format 2022-01-01 00:00:00
    await connection.query(
      `SELECT quid, quiz_name, description, 
        DATE_FORMAT(FROM_UNIXTIME(start_date), '%Y-%m-%d %H:%i:%s') as start_date 
       FROM savsoft_quiz 
       WHERE added_by = '${phone}'
       ORDER BY quid DESC
       LIMIT 10 OFFSET ${page * 10}`,
      async function (err, resu) {
        if (err) {
          res
            .status(500)
            .json({ error: "Internal Server Error", message: err });
          return;
        }
        res.status(200).json(resu);
      }
    );
  } catch (error) {
    console.error("Database error:", error);
    res
      .status(500)
      .json({ error: "Internal Server Error", message: error.message });
  }
});

app.get("/moderator/get-questions", verifyJwt, async (req, res) => {
  const { phone } = req;
  const { page } = req.query;
  try {
    await connection.query(
      ` SELECT qid, question 
        FROM nnekynbxen.savsoft_qbank
        WHERE added_by = '${phone}'
        ORDER BY qid DESC
        LIMIT 10 OFFSET ${page * 10}`,
      async function (err, resu) {
        if (err) {
          res
            .status(500)
            .json({ error: "Internal Server Error", message: err });
          return;
        }
        res.status(200).json(resu);
      }
    );
  } catch (error) {
    console.error("Database error:", error);
    res
      .status(500)
      .json({ error: "Internal Server Error", message: error.message });
  }
});



// Cloudflare Stream API integration with Signed URLs
const CLOUDFLARE_ACCOUNT_ID = "18aad3dec0d70ebfd4bb8d0f16ced4ec"; // Replace with your account ID
const CLOUDFLARE_API_TOKEN = "****************************************" // Add to your .env file

// Cloudflare R2 Configuration
const R2_ACCOUNT_ID =   "18aad3dec0d70ebfd4bb8d0f16ced4ec"; // Replace with your R2 account ID
const R2_ACCESS_KEY_ID =   "a746aa9e0f4cf86b90831bdb7d236571"; // Add to your .env file
const R2_SECRET_ACCESS_KEY =  "dbffee9e0e4850cf7fe42ce5aec6539a5c5e875c51b73de002b3ce4f7480f48f"; // Add to your .env file
const R2_BUCKET_NAME =   "nc-exams"; // Replace with your bucket name
const R2_ENDPOINT = `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`;

// Configure S3 Client for Cloudflare R2
const s3Client = new S3Client({
  region: "auto",
  endpoint: R2_ENDPOINT,
  credentials: {
    accessKeyId: R2_ACCESS_KEY_ID,
    secretAccessKey: R2_SECRET_ACCESS_KEY,
  },
});


 
// Function to enable signed URLs for a video
const enableSignedURLs = async (videoId) => {
  try {
    if (!CLOUDFLARE_API_TOKEN) {
      throw new Error("Cloudflare API token not configured. Please set CLOUDFLARE_API_TOKEN in your environment variables.");
    }

    if (!CLOUDFLARE_ACCOUNT_ID) {
      throw new Error("Cloudflare Account ID not configured. Please set CLOUDFLARE_ACCOUNT_ID in your environment variables.");
    }

    const response = await axios.patch(
      `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/stream/${videoId}`,
      {
        requireSignedURLs: true
      },
      {
        headers: {
          'Authorization': `Bearer ${CLOUDFLARE_API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.data.success) {
      console.error("Cloudflare API Error:", response.data.errors);
      throw new Error(`Cloudflare API Error: ${response.data.errors?.[0]?.message || 'Unknown error'}`);
    }

    return response.data.success;
  } catch (error) {
    console.error("Error enabling signed URLs:", error.response?.data || error.message);
    throw error;
  }
};

// Function to generate signed token using Cloudflare API (simpler method)
const generateSignedTokenAPI = async (videoId, restrictions = {}) => {
  try {
    if (!CLOUDFLARE_API_TOKEN) {
      throw new Error("Cloudflare API token not configured. Please set CLOUDFLARE_API_TOKEN in your environment variables.");
    }

    if (!CLOUDFLARE_ACCOUNT_ID) {
      throw new Error("Cloudflare Account ID not configured. Please set CLOUDFLARE_ACCOUNT_ID in your environment variables.");
    }

    // Default restrictions for security
    const defaultRestrictions = {
      exp: Math.floor(Date.now() / 1000) + (2 * 60 * 60), // 2 hours expiry
      downloadable: false, // Prevent downloads
      ...restrictions
    };

    const response = await axios.post(
      `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/stream/${videoId}/token`,
      defaultRestrictions,
      {
        headers: {
          'Authorization': `Bearer ${CLOUDFLARE_API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.data.success) {
      console.error("Cloudflare API Error:", response.data.errors);
      throw new Error(`Cloudflare API Error: ${response.data.errors?.[0]?.message || 'Failed to generate signed token'}`);
    }

    return {
      success: true,
      token: response.data.result.token,
      expiresAt: new Date(defaultRestrictions.exp * 1000).toISOString(),
      restrictions: defaultRestrictions
    };

  } catch (error) {
    console.error("Error generating signed token via API:", error.response?.data || error.message);
    throw error;
  }
};

// Function to get video credentials with signed URL
const getSecureVideoCredentials = async (videoId, userRestrictions = {}) => {
  try {
    if (!CLOUDFLARE_API_TOKEN) {
      throw new Error("Cloudflare API token not configured. Please set CLOUDFLARE_API_TOKEN in your environment variables.");
    }

    if (!CLOUDFLARE_ACCOUNT_ID) {
      throw new Error("Cloudflare Account ID not configured. Please set CLOUDFLARE_ACCOUNT_ID in your environment variables.");
    }

    // First, get video metadata
    const videoResponse = await axios.get(
      `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/stream/${videoId}`,
      {
        headers: {
          'Authorization': `Bearer ${CLOUDFLARE_API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!videoResponse.data.success) {
      console.error("Video fetch error:", videoResponse.data.errors);
      throw new Error(`Video not found: ${videoResponse.data.errors?.[0]?.message || 'Unknown error'}`);
    }

    const videoData = videoResponse.data.result;

    // Check if video is ready to stream
    if (!videoData.readyToStream) {
      throw new Error("Video is not ready for streaming");
    }

    // Enable signed URLs if not already enabled
    if (!videoData.requireSignedURLs) {
      await enableSignedURLs(videoId);
    }

    // Create watermark configuration
    const watermarkText = userRestrictions.phone || "Protected Content";
    console.log("Adding watermark with text:", watermarkText); // Debug log

    // Generate signed token with restrictions and watermark
    const tokenResult = await generateSignedTokenAPI(videoId, {
      exp: Math.floor(Date.now() / 1000) + (2 * 60 * 60), // 2 hours
      downloadable: false,
      allowSkip: true,
      allowFullscreen: true,
      allowPlaybackSpeedControl: true,
      watermark: {
        text: watermarkText,
        opacity: 0.5,
        size: 20,
        position: "bottom-right",
        color: "#FFFFFF"
      }
    });

    if (!tokenResult.success) {
      throw new Error("Failed to generate secure token");
    }

    // Get customer code
    let customerCode = '2vb3kzl37tmz0o9x';

    return {
      success: true,
      data: {
        thumbnail: videoData.thumbnail,
        secureIframeUrl: `https://customer-${customerCode}.cloudflarestream.com/${tokenResult.token}/iframe`,
        headers: {
          'Content-Security-Policy': "frame-ancestors 'self'",
          'X-Frame-Options': 'SAMEORIGIN',
          'X-Content-Type-Options': 'nosniff'
        }
      }
    };

  } catch (error) {
    console.error("Error getting secure video credentials:", error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data ? "Cloudflare API error" : "Network error",
      details: error.response?.data || error.message
    };
  }
};

// Express route handlers
const secureVideoCredentialsRoute = async (req, res) => {
  try {
    const { videoId } = req.params;
    const { phone } = req.query; // Get phone from query params
    
    // Validate videoId
    if (!videoId || typeof videoId !== 'string') {
      return res.status(400).json({
        success: false,
        error: "Invalid video ID",
        details: "Video ID is required and must be a valid string"
      });
    }

    // Validate phone number
    if (!phone) {
      return res.status(400).json({
        success: false,
        error: "Missing phone number",
        details: "Phone number is required for watermarking"
      });
    }

    // Check if API credentials are configured
    if (!CLOUDFLARE_API_TOKEN) {
      return res.status(500).json({
        success: false,
        error: "Server configuration error",
        details: "Cloudflare API credentials not configured"
      });
    }
    
    // Add user-specific restrictions
    const userRestrictions = {
      exp: Math.floor(Date.now() / 1000) + (2 * 60 * 60), // 2 hours
      phone: phone, // Pass phone for watermark
      // Add client IP for additional security
      ip: req.ip,
      // Add session-based restrictions
      session: req.sessionID,
      // Prevent downloads and restrict playback
      downloadable: false,
      allowSkip: false,
      allowFullscreen: false,
      allowPlaybackSpeedControl: false
    };

    const result = await getSecureVideoCredentials(videoId, userRestrictions);
    
    if (result.success) {
      // Set security headers
      res.set(result.data.headers);
      
      // Log access for security audit
      console.log(`Secure video access: User ${phone} accessed video ${videoId} at ${new Date().toISOString()}`);
      
      // Remove sensitive data before sending response
      delete result.data.headers;
      res.json(result);
    } else {
      let statusCode = 400;
      if (result.error === "Cloudflare API error") {
        statusCode = 502;
      } else if (result.error === "Network error") {
        statusCode = 503;
      }
      
      res.status(statusCode).json(result);
    }
  } catch (error) {
    console.error("Route error:", error);
    res.status(500).json({
      success: false,
      error: "Internal server error",
      details: process.env.NODE_ENV === 'development' ? error.message : "An unexpected error occurred"
    });
  }
};

 

// Test endpoint to verify Cloudflare API credentials
app.get("/test-cloudflare-auth", verifyJwt, verifyAdmin, async (req, res) => {
  try {
    if (!CLOUDFLARE_API_TOKEN) {
      return res.status(500).json({
        success: false,
        error: "Cloudflare API token not configured"
      });
    }

    if (!CLOUDFLARE_ACCOUNT_ID) {
      return res.status(500).json({
        success: false,
        error: "Cloudflare Account ID not configured"
      });
    }

    // Test API connection by listing videos (limited to 1)
    const response = await axios.get(
      `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/stream?limit=1`,
      {
        headers: {
          'Authorization': `Bearer ${CLOUDFLARE_API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.data.success) {
      res.json({
        success: true,
        message: "Cloudflare API credentials are valid",
        accountId: CLOUDFLARE_ACCOUNT_ID,
        videoCount: response.data.result_info?.total_count || 0
      });
    } else {
      res.status(401).json({
        success: false,
        error: "Invalid Cloudflare API credentials",
        details: response.data.errors
      });
    }
  } catch (error) {
    console.error("Cloudflare auth test error:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      error: "Failed to test Cloudflare API",
      details: error.response?.data || error.message
    });
  }
});

app.get("/get-secure-video/:videoId", verifyJwt, secureVideoCredentialsRoute);

// Google Drive PDF Management API
// Upload watermarked PDF to Google Drive and return download link
app.post("/upload-pdf-to-drive", verifyJwt, async (req, res) => {
  try {
    const { pdfData, fileName, quizId } = req.body;
    const userPhone = req.phone;

    console.log(`Google Drive upload request - User: ${userPhone}, Quiz: ${quizId}, File: ${fileName}`);
    console.log(`PDF data size: ${pdfData ? pdfData.length : 0} characters`);

    if (!pdfData || !fileName || !quizId) {
      return res.status(400).json({
        success: false,
        error: "Missing required parameters: pdfData, fileName, or quizId"
      });
    }

    // Validate base64 data
    if (typeof pdfData !== 'string' || pdfData.length === 0) {
      return res.status(400).json({
        success: false,
        error: "Invalid PDF data format"
      });
    }

    // Get access token for Google Drive API
    console.log('Getting Google Drive access token...');
    const accessToken = await getDriveAccessToken();

    // Convert base64 PDF data to buffer
    const pdfBuffer = Buffer.from(pdfData, 'base64');

    // Upload file to Google Drive
    console.log('Uploading file to Google Drive...');
    const file = await uploadFileToDrive(accessToken, fileName, pdfBuffer);

    console.log(`File uploaded to Google Drive with ID: ${file.id}`);

    // Make the file publicly accessible for download
    console.log('Setting file permissions...');
    await setDriveFilePermissions(accessToken, file.id);

    console.log('File permissions set successfully');

    // Get the direct download link
    const downloadLink = `https://drive.google.com/uc?export=download&id=${file.id}`;

    console.log(`PDF uploaded to Google Drive: ${fileName} by user: ${userPhone} for quiz: ${quizId}`);

    res.json({
      success: true,
      message: "PDF uploaded successfully to Google Drive",
      data: {
        fileId: file.id,
        fileName: fileName,
        downloadLink: downloadLink,
        webViewLink: file.webViewLink,
        quizId: quizId,
        uploadedBy: userPhone,
        uploadedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Google Drive upload error:', error);
    res.status(500).json({
      success: false,
      error: "Failed to upload PDF to Google Drive",
      details: error.message
    });
  }
});



// Delete PDF from Google Drive after download
app.delete("/delete-drive-pdf/:fileId", verifyJwt, async (req, res) => {
  try {
    const { fileId } = req.params;
    const userPhone = req.phone;

    if (!fileId) {
      return res.status(400).json({
        success: false,
        error: "File ID is required"
      });
    }

    // Get access token for Google Drive API
    console.log('Getting Google Drive access token...');
    const accessToken = await getDriveAccessToken();

    // Delete file from Google Drive
    await deleteFileFromDrive(accessToken, fileId);

    console.log(`PDF deleted from Google Drive: ${fileId} by user: ${userPhone}`);

    res.json({
      success: true,
      message: "PDF deleted successfully from Google Drive"
    });

  } catch (error) {
    console.error('Delete Drive PDF error:', error);
    res.status(500).json({
      success: false,
      error: "Failed to delete PDF from Google Drive",
      details: error.message
    });
  }
});

// Enhanced PDF Upload to Cloudflare R2 endpoint with database storage
app.post("/upload-pdf-to-r2", verifyJwt, pdfUpload.single("pdf"), async (req, res) => {
  try {
    // Check rate limit
    if (!checkRateLimit(req.phone, 'upload', 5, 15 * 60 * 1000)) {
      return res.status(429).json({
        success: false,
        error: "Rate limit exceeded. Please try again later."
      });
    }

    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: "No PDF file uploaded"
      });
    }

    // Validate required parameters
    const { quizId } = req.body;
    if (!quizId) {
      return res.status(400).json({
        success: false,
        error: "Quiz ID is required"
      });
    }

    // Validate file type
    if (req.file.mimetype !== 'application/pdf') {
      return res.status(400).json({
        success: false,
        error: "Only PDF files are allowed"
      });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const originalName = req.file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
    const fileName = `pdfs/${timestamp}_${randomString}_${originalName}`;

    // Prepare upload parameters for R2
    const uploadParams = {
      Bucket: R2_BUCKET_NAME,
      Key: fileName,
      Body: req.file.buffer,
      ContentType: req.file.mimetype,
      ContentLength: req.file.size,
      Metadata: {
        'uploaded-by': req.phone || 'unknown',
        'upload-date': new Date().toISOString(),
        'original-name': req.file.originalname
      }
    };

    // Upload to R2
    const command = new PutObjectCommand(uploadParams);
    await s3Client.send(command);

    // Generate public URL (for reference only - not for direct access)
    const publicUrl = `https://pub-${R2_ACCOUNT_ID}.r2.dev/${fileName}`;

    // Store file info in database
    const insertQuery = `
    INSERT INTO quiz_files (quiz_id, file_name, original_name, r2_key, file_size, uploaded_by, file_url)
      VALUES ('${quizId}', '${fileName}', '${req.file.originalname.replace(/'/g, "\\'")}', '${fileName}', ${req.file.size}, '${req.phone}', '${publicUrl}')
    `;

    let fileId;
    try {
      const result = await runQuery(insertQuery);
      fileId = result.insertId;
    } catch (dbError) {
      console.error("Database insert error:", dbError);
      // File uploaded to R2 but failed to save to database
      // In production, you might want to delete from R2 or implement cleanup
      return res.status(500).json({
        success: false,
        error: "File uploaded but failed to save metadata",
        details: "Database error occurred"
      });
    }

    // Log successful upload
    await logFileAccess(req.phone, quizId, fileName, 'upload', true);
    console.log(`PDF uploaded successfully to R2: ${fileName} by user: ${req.phone} for quiz: ${quizId}`);

    // Return success response
    res.json({
      success: true,
      message: "PDF uploaded successfully to Cloudflare R2",
      data: {
        fileId: fileId,
        quizId: quizId,
        fileName: fileName,
        originalName: req.file.originalname,
        size: req.file.size,
        r2Key: fileName,
        uploadedAt: new Date().toISOString(),
        uploadedBy: req.phone,
        // Note: Direct URL not provided for security - use /get-quiz-pdf/:quizId instead
        accessNote: "Use /get-quiz-pdf/" + quizId + " to get secure access URL"
      }
    });

  } catch (error) {
    console.error("R2 Upload Error:", error);

    // Log failed upload attempt
    await logFileAccess(req.phone, req.body.quizId || 'unknown', req.file?.originalname || 'unknown', 'upload', false, error.message);

    // Handle specific AWS/R2 errors
    if (error.name === 'NoSuchBucket') {
      return res.status(500).json({
        success: false,
        error: "R2 bucket not found. Please check bucket configuration.",
        details: "The specified bucket does not exist or is not accessible."
      });
    }

    if (error.name === 'InvalidAccessKeyId' || error.name === 'SignatureDoesNotMatch') {
      return res.status(500).json({
        success: false,
        error: "R2 authentication failed. Please check credentials.",
        details: "Invalid access key or secret key."
      });
    }

    if (error.name === 'AccessDenied') {
      return res.status(500).json({
        success: false,
        error: "Access denied to R2 bucket.",
        details: "Check your R2 API token permissions."
      });
    }

    // Generic error response
    res.status(500).json({
      success: false,
      error: "Failed to upload PDF to R2",
      details: error.message
    });
  }
});

// Secure File Retrieval API - Get temporary signed URL for quiz PDF
app.get("/get-quiz-pdf/:quizId", verifyJwt, async (req, res) => {
  try {
    const { quizId } = req.params;
    const userPhone = req.phone;

    // Check rate limit
    if (!checkRateLimit(userPhone, 'access', 20, 15 * 60 * 1000)) {
      await logFileAccess(userPhone, quizId, 'unknown', 'access', false, 'Rate limit exceeded');
      return res.status(429).json({
        success: false,
        error: "Rate limit exceeded. Please try again later."
      });
    }

    // Validate quizId
    if (!quizId) {
      return res.status(400).json({
        success: false,
        error: "Quiz ID is required"
      });
    }

    // Query database for file information
    const selectQuery = `
      SELECT id, quiz_id, file_name, original_name, r2_key, file_size, uploaded_by, upload_date, file_url
      FROM quiz_files
      WHERE quiz_id = '${quizId}'
      ORDER BY upload_date DESC
      LIMIT 1
    `;

    let fileRecord;
    try {
      const results = await runQuery(selectQuery);
      if (!results || results.length === 0) {
        await logFileAccess(userPhone, quizId, 'not_found', 'access', false, 'Quiz file not found');
        return res.status(404).json({
          success: false,
          error: "No PDF file found for this quiz",
          details: "The requested quiz does not have an associated PDF file"
        });
      }
      fileRecord = results[0];
    } catch (dbError) {
      console.error("Database query error:", dbError);
      await logFileAccess(userPhone, quizId, 'unknown', 'access', false, 'Database error');
      return res.status(500).json({
        success: false,
        error: "Database error occurred",
        details: "Failed to retrieve file information"
      });
    }

    // Check user permissions (user must be the uploader or admin)
    const admins = {
      **********: true,
      **********: true,
    };

    const isAdmin = admins[parseInt(userPhone)];
    const isOwner = fileRecord.uploaded_by === userPhone;

    if (!isAdmin && !isOwner) {
      await logFileAccess(userPhone, quizId, fileRecord.file_name, 'access', false, 'Unauthorized access attempt');
      return res.status(403).json({
        success: false,
        error: "Access denied",
        details: "You don't have permission to access this file"
      });
    }

    // Generate signed URL with 15-minute expiration
    const getObjectParams = {
      Bucket: R2_BUCKET_NAME,
      Key: fileRecord.r2_key,
    };

    let signedUrl;
    try {
      const command = new GetObjectCommand(getObjectParams);
      signedUrl = await getSignedUrl(s3Client, command, {
        expiresIn: 15 * 60 // 15 minutes in seconds
      });
    } catch (r2Error) {
      console.error("R2 signed URL generation error:", r2Error);
      await logFileAccess(userPhone, quizId, fileRecord.file_name, 'access', false, 'R2 error: ' + r2Error.message);

      if (r2Error.name === 'NoSuchKey') {
        return res.status(404).json({
          success: false,
          error: "File not found in storage",
          details: "The file exists in database but was not found in R2 storage"
        });
      }

      return res.status(500).json({
        success: false,
        error: "Failed to generate secure access URL",
        details: "R2 service error occurred"
      });
    }

    // Log successful access
    await logFileAccess(userPhone, quizId, fileRecord.file_name, 'access', true);

    // Return signed URL and file information
    res.json({
      success: true,
      message: "Secure access URL generated successfully",
      data: {
        fileId: fileRecord.id,
        quizId: fileRecord.quiz_id,
        fileName: fileRecord.file_name,
        originalName: fileRecord.original_name,
        fileSize: fileRecord.file_size,
        uploadedBy: fileRecord.uploaded_by,
        uploadDate: fileRecord.upload_date,
        secureUrl: signedUrl,
        expiresIn: "15 minutes",
        expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString()
      }
    });

  } catch (error) {
    console.error("Secure file retrieval error:", error);
    await logFileAccess(req.phone, req.params.quizId, 'unknown', 'access', false, error.message);

    res.status(500).json({
      success: false,
      error: "Failed to retrieve secure file access",
      details: error.message
    });
  }
});

// Admin API - Check if PDF file exists for a quiz
app.get("/admin/check-quiz-pdf/:quizId", verifyJwt, verifyAdmin, async (req, res) => {
  try {
    const { quizId } = req.params;

    // Validate quizId
    if (!quizId) {
      return res.status(400).json({
        success: false,
        error: "Quiz ID is required"
      });
    }

    // Query database for file information
    const selectQuery = `
      SELECT id, quiz_id, file_name, original_name, r2_key, file_size, uploaded_by, upload_date, file_url
      FROM quiz_files
      WHERE quiz_id = '${quizId}'
      ORDER BY upload_date DESC
      LIMIT 1
    `;

    let fileRecord;
    try {
      const results = await runQuery(selectQuery);
      if (!results || results.length === 0) {
        return res.json({
          success: true,
          message: "No PDF file found for this quiz",
          data: {
            hasFile: false,
            quizId: quizId
          }
        });
      }
      fileRecord = results[0];
    } catch (dbError) {
      console.error("Database query error:", dbError);
      return res.status(500).json({
        success: false,
        error: "Database error occurred",
        details: "Failed to retrieve file information"
      });
    }

    // Return file information
    res.json({
      success: true,
      message: "PDF file found for this quiz",
      data: {
        hasFile: true,
        fileId: fileRecord.id,
        quizId: fileRecord.quiz_id,
        fileName: fileRecord.file_name,
        originalName: fileRecord.original_name,
        fileSize: fileRecord.file_size,
        uploadedBy: fileRecord.uploaded_by,
        uploadDate: fileRecord.upload_date,
        r2Key: fileRecord.r2_key
      }
    });

  } catch (error) {
    console.error("Admin check PDF file error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to check PDF file status",
      details: error.message
    });
  }
});

// Admin API - Delete PDF file from R2 and database
app.delete("/admin/delete-quiz-pdf/:quizId", verifyJwt, verifyAdmin, async (req, res) => {
  try {
    const { quizId } = req.params;
    const adminPhone = req.phone;

    // Validate quizId
    if (!quizId) {
      return res.status(400).json({
        success: false,
        error: "Quiz ID is required"
      });
    }

    // First, get the file information from database
    const selectQuery = `
      SELECT id, quiz_id, file_name, original_name, r2_key, file_size, uploaded_by, upload_date
      FROM quiz_files
      WHERE quiz_id = '${quizId}'
      ORDER BY upload_date DESC
      LIMIT 1
    `;

    let fileRecord;
    try {
      const results = await runQuery(selectQuery);
      if (!results || results.length === 0) {
        return res.status(404).json({
          success: false,
          error: "No PDF file found for this quiz",
          details: "The requested quiz does not have an associated PDF file"
        });
      }
      fileRecord = results[0];
    } catch (dbError) {
      console.error("Database query error:", dbError);
      return res.status(500).json({
        success: false,
        error: "Database error occurred",
        details: "Failed to retrieve file information"
      });
    }

    // Delete from R2 storage
    try {
      const deleteParams = {
        Bucket: R2_BUCKET_NAME,
        Key: fileRecord.r2_key
      };

      const deleteCommand = new DeleteObjectCommand(deleteParams);
      await s3Client.send(deleteCommand);

      console.log(`File deleted from R2: ${fileRecord.r2_key}`);
    } catch (r2Error) {
      console.error("R2 deletion error:", r2Error);

      // If file doesn't exist in R2, we'll still proceed to delete from database
      if (r2Error.name !== 'NoSuchKey') {
        await logFileAccess(adminPhone, quizId, fileRecord.file_name, 'delete', false, 'R2 deletion failed: ' + r2Error.message);
        return res.status(500).json({
          success: false,
          error: "Failed to delete file from storage",
          details: "R2 service error occurred"
        });
      }
    }

    // Delete from database
    try {
      const deleteQuery = `DELETE FROM quiz_files WHERE id = ${fileRecord.id}`;
      await runQuery(deleteQuery);

      console.log(`File record deleted from database: ID ${fileRecord.id}`);
    } catch (dbError) {
      console.error("Database deletion error:", dbError);
      await logFileAccess(adminPhone, quizId, fileRecord.file_name, 'delete', false, 'Database deletion failed: ' + dbError.message);
      return res.status(500).json({
        success: false,
        error: "Failed to delete file record from database",
        details: "Database error occurred"
      });
    }

    // Log successful deletion
    await logFileAccess(adminPhone, quizId, fileRecord.file_name, 'delete', true, `File deleted by admin: ${adminPhone}`);

    // Return success response
    res.json({
      success: true,
      message: "PDF file deleted successfully from both storage and database",
      data: {
        deletedFile: {
          fileId: fileRecord.id,
          quizId: fileRecord.quiz_id,
          fileName: fileRecord.file_name,
          originalName: fileRecord.original_name,
          fileSize: fileRecord.file_size,
          uploadedBy: fileRecord.uploaded_by,
          uploadDate: fileRecord.upload_date,
          deletedBy: adminPhone,
          deletedAt: new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error("Admin delete PDF file error:", error);
    await logFileAccess(req.phone, req.params.quizId, 'unknown', 'delete', false, error.message);

    res.status(500).json({
      success: false,
      error: "Failed to delete PDF file",
      details: error.message
    });
  }
});

server.listen(4002, () => console.log("port running"));
