import React from "react";
import Button from "@mui/material/Button";
import { Link } from "react-router-dom";
import <PERSON><PERSON> from "js-cookie";
import invalid from "../invalid.png";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import AdminMenu from "./AdminMenu";
import commonData from "../../importanValue";
import sendpush from "./sendNotification";
import CloudflareStreamManager from "./CloudflareStreamManager";

import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import CryptoJS from "crypto-js";
import "react-notifications/lib/notifications.css";
import "./styles.css";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
} from "@mui/material";
import Editor from "./Editor";
import QuizIcon from '@mui/icons-material/Quiz';
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';

class AddNewExam extends React.Component {
  // Encryption key - in production, this should be from environment variables
  encryptionKey = "NcExams2024SecretKey!@#";

  // Encrypt function for notes content
  encryptContent = (text) => {
    try {
      const encrypted = CryptoJS.AES.encrypt(text, this.encryptionKey).toString();
      return encrypted;
    } catch (error) {
      console.error('Encryption failed:', error);
      return text; // Return original text if encryption fails
    }
  };

  // Decrypt function for notes content
  decryptContent = (encryptedText) => {
    try {
      if (!encryptedText || encryptedText.trim() === '') {
        return '';
      }
      
      // console.log('Attempting to decrypt:', encryptedText.substring(0, 50) + '...');
      const decrypted = CryptoJS.AES.decrypt(encryptedText, this.encryptionKey).toString(CryptoJS.enc.Utf8);
      // console.log('Decryption result:', decrypted ? 'Success' : 'Failed');
      
      if (decrypted && decrypted.trim() !== '') {
        return decrypted;
      } else {
        // If decryption resulted in empty string, it might not be encrypted content
        // console.log('Decryption returned empty, treating as plain text');
        return encryptedText;
      }
    } catch (error) {
      console.error('Decryption failed:', error);
      return encryptedText; // Return original text if decryption fails
    }
  };

  state = {
    login: "valid",
    showAdvancedOptions: false,
    name: "",
    description: "",
    isSinglePaidExam: false,
    examPrice: "",
    startdate:
      new Date().getFullYear() +
      "-" +
      (parseInt(new Date().getMonth()) + parseInt(1)) +
      "-" +
      new Date().getDate() +
      " 09:00:00",
    enddate:
      new Date().getFullYear() +
      parseInt(10) +
      "-" +
      (parseInt(new Date().getMonth()) + parseInt(1)) +
      "-" +
      parseInt(new Date().getDate()) +
      " 09:00:00",
    duration: "0",
    attempts: 5,
    minPercent: 60,
    addType: "manual",
    isLoading: false,
    packages: [],
    selectedGids: [],
    selectedqids: [],
    showQuestionAddPage: false,
    questionsallData: [],
    questionsCount: 0,
    page: 0,
    categoryList: [],
    descriptionText: "",
    examid: 0,
    selectedQData: [],
    type: "Add",
    showQuestions: true,
    multipleSelectUsed: false,
    dataPerPage: "25",
    open: "1",
    selectedCid: "",
    selectedCidMaxCount: 0,
    selectedCidSelectedValue: 0,
    categoryString: "",
    isPdfPackage: false,

    qCount: null,
    qReportData: [],
    popUpOpen: false,
    qNum: null,
    category: null,
    questionText: "",
    op1: "",
    op2: "",
    op3: "",
    op4: "",
    op5: "",
    op1Num: 0,
    op2Num: 0,
    op3Num: 0,
    op4Num: 0,
    op5Num: 0,
    correctOption: 1,
    selectedCat: "",

    editorLoading: false,
    optionsData: [],
    popupType: null,
    search: "",

    oid: 0,
    addOptionClicked: false,
    errQdata: [],
    ErrqNum: 0,
    catId: 0,
    day: null,
    prevSearch: "",
    videos: false,
    cloudflareVideoId: null,
    cloudflareVideoUrl: null,
    pdfContentType: "notes", // "link" or "notes" (notes includes PDF upload)
    uploadedPdfFile: null,
    uploadedPdfUrl: null,
    isUploadingPdf: false,
    uploadProgress: 0,
    existingPdfFile: null,
    isCheckingPdf: false,
    isDeletingPdf: false,
  };

  componentDidMount() {
    this.loadData();
  }

  loadData = () => {
    const { match } = this.props;
    const { params } = match;
    const { examid } = params;
    const { location } = this.props;
    const { search } = location;
    this.setState({
      isPdfPackage: search === "?type=pdfs" || search === "?type=videos",
      prevSearch: search,
      videos: search === "?type=videos",
    });
    // console.log(examid);
    if (examid === "0") {
      this.getPackagesList();
    } else {
      this.examsDetails();
    }
    this.getPackagesList();
  };
  examsDetails = async () => {
    const { match } = this.props;
    const { params } = match;
    const { examid } = params;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "editexam",
      search: "",
      qid: examid,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      // console.log(data);
      const exam = data.data[0][0];
      
      // Determine content type for PDF packages based on existing description
      let pdfContentType = "notes"; // Default to notes since it includes PDF upload
      let processedDescription = exam.description;

      // Only decrypt description if type is PDF (not videos)
      if (this.state.isPdfPackage && !this.state.videos && exam.description) {
        // console.log('Original description:', exam.description);

        // Check if description looks like a URL first
        const urlPattern = /^https?:\/\/|^www\.|^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;
        const isUrl = urlPattern.test(exam.description.trim());

        if (isUrl) {
          // It's a URL link
          pdfContentType = "link";
          processedDescription = exam.description;
          // console.log('Detected as URL link');
        } else {
          // Not a URL, try to decrypt it (might be encrypted notes)
          const decryptedContent = this.decryptContent(exam.description);
          // console.log('Decrypted content:', decryptedContent);

          // If decryption changed the content and resulted in meaningful text, it was encrypted
          if (decryptedContent !== exam.description && decryptedContent.trim() !== '') {
            pdfContentType = "notes";
            processedDescription = decryptedContent;
            // console.log('Detected as encrypted notes, decrypted successfully');
          } else {
            // Not encrypted, check if it has HTML tags (formatted notes) or default to notes
            pdfContentType = "notes";
            processedDescription = exam.description || '';
            // console.log('Detected as plain text notes or empty');
          }
        }
      }
      
      // console.log('Setting state with:', {
      //   pdfContentType,
      //   processedDescription,
      //   originalDescription: exam.description
      // });

      this.setState({
        examid: examid,
        selectedQData: (data.data[1].length>0 &&data.data[1][0].result === "" && []) || data.data[1],
        name: exam.quiz_name,
        descriptionText: processedDescription,
        description: processedDescription,
        startdate: exam.startDate,
        enddate: exam.endDate,
        day: exam.day,
        duration: exam.duration,
        attempts: exam.maximum_attempts,
        minPercent: exam.pass_percentage,
        addType: exam.question_selection === 0 ? "manual" : "automatic",
        selectedGids: exam.gids.split(","),
        selectedqids: exam.qids !== "" ? exam.qids.split(",") : [],
        isLoading: false,
        type: "Edit",
        open: exam.with_login,
        categoryList: data.data[2],
        categoryString: exam.autoselect !== null ? exam.autoselect : "",
        isSinglePaidExam: exam.is_single_paid_exam === "1",
        examPrice: exam.price,
        pdfContentType: pdfContentType,
      });
      
      // Check for existing video after setting the description
      this.checkForExistingVideo();

      // Check for existing PDF file if this is a PDF package
      if (this.state.isPdfPackage && !this.state.videos) {
        this.checkForExistingPdf();
      }
    } catch (err) {
      console.log(err,'err')
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  getPackagesList = async () => {
    const { location } = this.props;
    const { search } = location;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    try {
      this.setState({ isLoading: true });
      const data = await axios.get(
        `${commonData["api"]}/adminmasterdata/${
          search === "?type=pdfs"
            ? "getPDFPackages"
            : search === "?type=videos"
            ? "getVideoPackages"
            : "getPackages"
        }`,
        { headers }
      );
      // console.log(data.data);
      this.setState({ isLoading: false, packages: data.data[0] });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  onChangeState = (e) => {
    e.preventDefault();
    this.setState({ [e.target.id]: e.target.value });
  };

  handlePdfContentTypeChange = (newType) => {
    const { description, pdfContentType } = this.state;

    // If switching from notes to link and description contains HTML tags, clear it
    if (pdfContentType === "notes" && newType === "link" && description) {
      const hasHtmlTags = /<[^>]*>/g.test(description);
      if (hasHtmlTags) {
        // Ask user if they want to clear the formatted content
        const shouldClear = window.confirm(
          "Switching to PDF Link will clear your formatted notes content. Continue?"
        );
        if (shouldClear) {
          this.setState({
            pdfContentType: newType,
            description: ""
          });
        }
        return;
      }
    }

    // If switching from link to notes and description looks like a URL, clear it
    if (pdfContentType === "link" && newType === "notes" && description) {
      const urlPattern = /^https?:\/\/|^www\.|^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;
      if (urlPattern.test(description.trim())) {
        // Ask user if they want to clear the link
        const shouldClear = window.confirm(
          "Switching to Notes Text will clear your PDF link. Continue?"
        );
        if (shouldClear) {
          this.setState({
            pdfContentType: newType,
            description: ""
          });
        }
        return;
      }
    }

    // Otherwise, just switch the type
    this.setState({ pdfContentType: newType });
  };

  // Handle PDF file selection
  handlePdfFileSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
      this.setState({
        uploadedPdfFile: file,
        uploadedPdfUrl: null
      });
    } else if (file) {
      NotificationManager.error("Please select a valid PDF file");
    }
  };

  // Handle PDF upload to R2
  handlePdfUpload = async () => {
    const { uploadedPdfFile } = this.state;
    const token = Cookie.get("jwt_token");

    if (!uploadedPdfFile) {
      NotificationManager.error("Please select a PDF file first");
      return;
    }

    // Set uploading state
    this.setState({
      isUploadingPdf: true,
      uploadProgress: 0
    });

    try {
      // Create form data
      const formData = new FormData();
      formData.append('pdf', uploadedPdfFile);
      formData.append('quizId', this.state.examid || 'temp_' + Date.now());

      // Create axios request with progress tracking
      const response = await axios.post(
        `${commonData["api"]}/upload-pdf-to-r2`,
        formData,
        {
          headers: {
            'Authorization': token,
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            this.setState({ uploadProgress: percentCompleted });
          }
        }
      );

      if (response.data.success) {
        // Update state with the uploaded file URL (but don't store in description)
        this.setState({
          uploadedPdfUrl: response.data.data.url,
          isUploadingPdf: false,
          existingPdfFile: null // Clear existing file since we just replaced it
        });

        NotificationManager.success("PDF uploaded successfully and stored separately from notes");

        // Refresh the existing PDF file info
        setTimeout(() => {
          this.checkForExistingPdf();
        }, 1000);
      } else {
        throw new Error(response.data.error || "Upload failed");
      }
    } catch (error) {
      console.error("PDF upload error:", error);
      NotificationManager.error(error.message || "Failed to upload PDF");
      this.setState({ isUploadingPdf: false });
    }
  };

  // Reset PDF upload (keep notes content)
  handlePdfReset = () => {
    this.setState({
      uploadedPdfFile: null,
      uploadedPdfUrl: null,
      isUploadingPdf: false,
      uploadProgress: 0
    });
  };

  // Check for existing PDF file
  checkForExistingPdf = async () => {
    const { examid } = this.state;

    if (!examid || examid === "0") return;

    this.setState({ isCheckingPdf: true });

    try {
      const token = Cookie.get("jwt_token");
      const response = await axios.get(
        `${commonData["api"]}/admin/check-quiz-pdf/${examid}`,
        {
          headers: {
            Authorization: token,
          },
        }
      );

      if (response.data.success && response.data.data.hasFile) {
        this.setState({
          existingPdfFile: response.data.data,
          isCheckingPdf: false
        });
      } else {
        this.setState({
          existingPdfFile: null,
          isCheckingPdf: false
        });
      }
    } catch (error) {
      console.error("Check PDF file error:", error);
      this.setState({
        existingPdfFile: null,
        isCheckingPdf: false
      });
    }
  };

  // Delete existing PDF file
  handleDeleteExistingPdf = async () => {
    const { examid } = this.state;

    if (!window.confirm("Are you sure you want to delete the existing PDF file? This action cannot be undone.")) {
      return;
    }

    this.setState({ isDeletingPdf: true });

    try {
      const token = Cookie.get("jwt_token");
      const response = await axios.delete(
        `${commonData["api"]}/admin/delete-quiz-pdf/${examid}`,
        {
          headers: {
            Authorization: token,
          },
        }
      );

      if (response.data.success) {
        NotificationManager.success("PDF file deleted successfully");
        this.setState({
          existingPdfFile: null,
          isDeletingPdf: false
        });
      } else {
        throw new Error(response.data.error || "Failed to delete PDF file");
      }
    } catch (error) {
      console.error("Delete PDF file error:", error);
      this.setState({ isDeletingPdf: false });
      NotificationManager.error(error.response?.data?.error || "Failed to delete PDF file");
    }
  };

  // Download existing PDF file
  handleDownloadExistingPdf = async () => {
    const { examid } = this.state;

    try {
      NotificationManager.info("Generating secure download link...");

      const token = Cookie.get("jwt_token");
      const response = await axios.get(
        `${commonData["api"]}/get-quiz-pdf/${examid}`,
        {
          headers: {
            Authorization: token,
          },
        }
      );

      if (response.data.success) {
        const { secureUrl, originalName } = response.data.data;

        // Check if running in Android WebView
        const isAndroidWebView = /Android.*wv\)|; wv\)/i.test(navigator.userAgent) ||
                                 window.AndroidInterface !== undefined ||
                                 /Android/i.test(navigator.userAgent) ||
                                 window.webkit?.messageHandlers !== undefined;

        if (isAndroidWebView) {
          // For Android WebView, use a different approach
          try {
            // Method 1: Try using Android interface if available
            if (window.AndroidInterface && typeof window.AndroidInterface.downloadFile === 'function') {
              console.log('Admin using AndroidInterface for download');
              window.AndroidInterface.downloadFile(secureUrl, originalName);
              NotificationManager.success("PDF download started via app");
              return;
            }

            // Method 2: Try iOS WebKit message handler
            if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.downloadFile) {
              console.log('Admin using iOS WebKit for download');
              window.webkit.messageHandlers.downloadFile.postMessage({
                url: secureUrl,
                filename: originalName
              });
              NotificationManager.success("PDF download started via app");
              return;
            }

            // Method 3: Open in new window/tab for WebView
            console.log('Admin trying to open in new window');
            const newWindow = window.open(secureUrl, '_blank');
            if (newWindow) {
              NotificationManager.success("PDF opened in new tab. Please save from there.");
            } else {
              // Method 4: Navigate to the URL directly
              console.log('Admin navigating to URL directly');
              NotificationManager.info("Redirecting to PDF. Use browser back button to return.");
              setTimeout(() => {
                window.location.href = secureUrl;
              }, 1000);
            }
          } catch (error) {
            console.error('WebView download error:', error);
            // Fallback: try standard download
            this.standardAdminDownload(secureUrl, originalName);
          }
        } else {
          // Standard browser download
          this.standardAdminDownload(secureUrl, originalName);
        }

        NotificationManager.success("PDF download started");
      } else {
        throw new Error(response.data.error || "Failed to get download link");
      }
    } catch (error) {
      console.error("Download PDF file error:", error);
      NotificationManager.error(error.response?.data?.error || "Failed to download PDF file");
    }
  };

  // Format file size helper
  formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date helper
  formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  // Standard download method for admin
  standardAdminDownload = (url, fileName) => {
    try {
      const downloadLink = document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = fileName;
      downloadLink.style.display = 'none';

      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    } catch (error) {
      console.error('Standard admin download error:', error);
      // Final fallback: open in new window
      window.open(url, '_blank');
    }
  };

  showQuestionAdd = () => {
    const { name, description, selectedGids } = this.state;
    if (name === "") {
      NotificationManager.error(`Please Enter Exam Name`);
    } else if (description === "") {
      NotificationManager.error(`Please Enter Description`);
    } else if (selectedGids.length === 0) {
      NotificationManager.error(`Please select atleast one Package`);
    } else {
      this.setState({ showQuestionAddPage: true });
      this.getData();
    }
  };

  getData = async () => {
    const { page, dataPerPage } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "qbankallAddQuestions",
      search: dataPerPage,
      qid: page * dataPerPage,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      // console.log(data);
      this.setState({
        isLoading: false,
        questionsallData: data.data[0],
        categoryList: data.data[1],
        questionsCount: data.data[2][0].count,
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  renderPaginationButtons = (totalCount) => {
    const paidcount = Math.ceil(totalCount);
    const { page } = this.state;
    // console.log(paidrows);
    return (
      <div className="pagination">
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page - 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === 0}
        >
          Back
        </Button>
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page + 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === paidcount}
        >
          Next
        </Button>
      </div>
    );
  };
  renderAdvancedOptions = () => {
    const {
      addType,
      enddate,
      minPercent,
      duration,
      attempts,
      open,
      isPdfPackage,
    } = this.state;
    return (
      <>
        {!isPdfPackage && (
          <>
            <div>
              Duration in Min.
              <br />
              <input
                type="number"
                id="duration"
                className="input-box-add-exam"
                value={duration}
                onChange={this.onChangeState}
              />
            </div>
            <div>
              Allow Maximum Attempts <br />
              <input
                type="number"
                id="attempts"
                className="input-box-add-exam"
                value={attempts}
                onChange={this.onChangeState}
              />
            </div>
            <div>
              Minimum Percentage Required to Pass <br />
              <input
                type="text"
                id="minPercent"
                className="input-box-add-exam"
                value={minPercent}
                onChange={this.onChangeState}
              />
            </div>

            <div style={{ marginTop: 10 }}>
              Open Exam?
              <br />
              <input
                type="radio"
                name="open"
                id="open1"
                value="0"
                onChange={() => this.setState({ open: "0" })}
                checked={open === "0"}
              />{" "}
              <label htmlFor="open1" style={{ cursor: "pointer" }}>
                Yes
              </label>
              <input
                type="radio"
                name="open"
                id="open0"
                value="1"
                onChange={() => this.setState({ open: "1" })}
                checked={open === "1"}
                style={{ marginLeft: 20 }}
              />{" "}
              <label htmlFor="open0" style={{ cursor: "pointer" }}>
                No
              </label>
            </div>

            <div>
              Show Result
              <br />
              <div style={{ display: "flex", flexDirection: "row" }}>
                <input
                  type="radio"
                  name="add_Type"
                  id="manual"
                  value="manual"
                  onChange={() => this.setState({ addType: "manual" })}
                  checked={addType === "manual"}
                />{" "}
                <label
                  htmlFor="manual"
                  style={{ cursor: "pointer", marginRight: 15 }}
                >
                  Yes
                </label>
                <br />
                <input
                  type="radio"
                  name="add_Type"
                  id="automatic"
                  value="automatic"
                  onChange={() => this.setState({ addType: "automatic" })}
                  checked={addType === "automatic"}
                />{" "}
                <label
                  htmlFor="automatic"
                  style={{ cursor: "pointer", marginRight: 15 }}
                >
                  No
                </label>
              </div>
            </div>
          </>
        )}
      </>
    );
  };
  getQuestionNums = async (QuestionsCount, QuestionsLast) => {
    const {
      selectedCid,
      multipleSelectUsed,
      selectedCidSelectedValue,
      categoryString,
    } = this.state;

    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "qbankNumsmultiselect",
      search: selectedCid,
      qid: QuestionsCount + "$$$" + QuestionsLast,
    };
    console.log(body);
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );

      const prevselectedqids = this.state.selectedqids;

      const newqids = prevselectedqids.concat(data.data[0][0].qids.split(","));

      this.setState({
        isLoading: false,
        selectedqids: newqids,
        multipleSelectUsed: true,
        categoryString:
          categoryString !== ""
            ? categoryString +
              "," +
              selectedCid +
              "_" +
              selectedCidSelectedValue
            : selectedCid + "_" + selectedCidSelectedValue,
        selectedCid: "",
        selectedCidSelectedValue: 0,
        selectedCidMaxCount: 0,
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  addMultipleQues = () => {
    const QuestionsCount = prompt("Please enter start Question Id");
    const QuestionsLast = prompt("Please enter end Question Id");
    if (
      QuestionsCount === "" ||
      isNaN(QuestionsCount) ||
      QuestionsCount <= 0 ||
      QuestionsLast <= 0 ||
      QuestionsLast === ""
    ) {
      NotificationManager.error(`Please Enter Valid value`);
    } else {
      this.setState({ multipleSelectUsed: true }, () =>
        this.getQuestionNums(QuestionsCount, QuestionsLast)
      );
    }
  };
  addQuestionPage = () => {
    const {
      questionsCount,
      questionsallData,
      categoryList,
      selectedqids,
      selectedQData,
    } = this.state;
    // console.log(selectedqids);
    const style = `
    table {
      font-family: arial, sans-serif;
      border-collapse: collapse;
      width: 100%;
    }
    
    td,
    th {
      border: 1px solid #dddddd;
      text-align: left;
      padding: 10px;
      height: "100%";
    }
    
    tr:nth-child(even) {
      background-color: #dddddd;
    }`;
    return (
      <div className="addnewExam">
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: 10,
          }}
        >
          <div
            style={{
              marginTop: -10,
            }}
          >
            <Button
              className="btn navigate"
              onClick={() =>
                this.setState({
                  showQuestionAddPage: false,
                  dataPerPage: "25",
                  duration: selectedqids.length,
                })
              }
            >
              Back
            </Button>
          </div>
          <h3>Add Questions into Exam</h3>

          <div style={{ cursor: "pointer" }} onClick={this.addMultipleQues}>
            <i className="bi bi-plus-circle"></i> Add Questions
          </div>

          <div style={{ display: "flex" }}>
            No.of Questions added{" "}
            <div
              style={{
                backgroundColor: "black",
                borderRadius: 40,
                width: 30,
                height: 30,
                color: "#FFF",
                marginLeft: 5,
                textAlign: "center",
              }}
            >
              {selectedqids.length}
            </div>
          </div>
        </div>
        <div className="showbtnsdiv">
          <Button
            className="btn navigate"
            onClick={() => this.setState({ dataPerPage: "25" }, this.getData)}
          >
            Show 25
          </Button>
          <Button
            className="btn navigate"
            onClick={() => this.setState({ dataPerPage: "50" }, this.getData)}
          >
            Show 50
          </Button>
          <Button
            className="btn navigate"
            onClick={() => this.setState({ dataPerPage: "100" }, this.getData)}
          >
            Show 100
          </Button>

          <Button
            className="btn navigate"
            onClick={() => this.setState({ dataPerPage: "150" }, this.getData)}
          >
            Show 150
          </Button>
        </div>
        <style>{style}</style>
        <table>
          <thead>
            <tr>
              <th>Question</th>
              <th>Category</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {questionsallData.map((e, i) => {
              return (
                <tr key={"addnewQuizQuestions" + e.questionid}>
                  <td>
                    <div style={{ display: "flex", height: "100%" }}>
                      {e.questionid + ") "}
                      <span
                        style={{ marginLeft: 5 }}
                        dangerouslySetInnerHTML={{ __html: e.question }}
                      ></span>
                    </div>
                  </td>

                  <td>
                    {
                      categoryList.filter(
                        (val) => parseInt(val.cid) === parseInt(e.cid)
                      )[0].category_name
                    }
                  </td>
                  <td
                    onClick={(ev) => {
                      // console.log(selectedqids);
                      if (selectedqids.indexOf(String(e.questionid)) === -1) {
                        const newgroup = selectedqids;
                        newgroup.push(String(e.questionid));
                        const newQuestions = selectedQData;
                        newQuestions.push({
                          category_name: categoryList.filter(
                            (val) => parseInt(val.cid) === parseInt(e.cid)
                          )[0].category_name,
                          cid: e.cid,
                          qid: e.questionid,
                          question: e.question,
                        });
                        this.setState({
                          selectedqids: newgroup,
                          selectedQData: newQuestions,
                          duration: newgroup.length,
                        });
                      } else {
                        const newgroup = selectedqids.filter(
                          (v) => v !== String(e.questionid)
                        );
                        const newQuestions = selectedQData.filter(
                          (v2) => v2.qid !== e.questionid
                        );

                        this.setState({
                          selectedqids: newgroup,
                          selectedQData: newQuestions,
                          duration: newgroup.length,
                        });
                      }
                    }}
                    style={{ cursor: "pointer" }}
                  >
                    {" "}
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        height: "100%",
                      }}
                    >
                      <i
                        className={`bi ${
                          selectedqids.indexOf(String(e.questionid)) === -1
                            ? "bi-plus-circle"
                            : "bi-trash-fill"
                        } `}
                      ></i>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
        {this.renderPaginationButtons(questionsCount)}
      </div>
    );
  };

  addNewExamFunc = () => {
    const {
      showAdvancedOptions,
      name,
      packages,
      startdate,
      description,
      selectedGids,
      descriptionText,
      examid,
      isLoading,
      selectedqids,
      isPdfPackage,
      open,
      enddate,
      day,
      isSinglePaidExam,
      examPrice,
      videos,
    } = this.state;
    console.log(descriptionText,'descriptionText');
    return (
      <div className="addnewExam">
        <div>
          {!isPdfPackage && (
            <h2>{examid !== 0 ? "Edit Exam" : "Add New Exam"}</h2>
          )}
          {isPdfPackage && (
            <h2>{examid !== 0 ? "Edit PDF/Video" : "Add New PDF/Video"}</h2>
          )}
        </div>
        <div>
          {isPdfPackage ? "PDF/Video" : "Exam"} Name{" "}
          {open === "0" && examid !== 0 && (
            <i
              class="bi bi-clipboard"
              onClick={() => {
                navigator.clipboard.writeText(
                  `${commonData["app"]}/exam-details/${examid}`
                );
                NotificationManager.success("Exam Link Copied to ClipBoard");
              }}
              style={{ cursor: "pointer", marginLeft: 15 }}
            >
              {" "}
              Copy Exam Link
            </i>
          )}
          <br />
          <input
            type="text"
            id="name"
            className="input-box-add-exam"
            value={name}
            onChange={this.onChangeState}
          />
        </div>
        {!isPdfPackage  && (
          <div>
            Syllabus
            <Editor
              key={`syllabus-editor-${this.state.examid}`}
              initialValue={descriptionText}
              init={{
                height: 200,
                width: "80%",
                menubar: false,

                plugins: [
                  "advlist autolink lists link image charmap print preview anchor",
                  "searchreplace visualblocks code fullscreen",
                  "insertdatetime media table paste code help wordcount",
                ],
                toolbar:
                  "undo redo | formatselect | " +
                  "bold italic backcolor | alignleft aligncenter " +
                  "alignright | bullist numlist | subscript superscript |  " +
                  " code " +
                  "removeformat",
                content_style:
                  "body { font-family:Ramabhadra; font-size:14px }",
                setup: (editor) => {
                  editor.on('init', () => {
                    // Set cursor at the end of content
                    editor.selection.select(editor.getBody(), true);
                    editor.selection.collapse(false);
                  });
                }
              }}
              onEditorChange={(e) => this.setState({ description: e })}
            />
          </div>
        )}
        {isPdfPackage && videos && this.renderVideoUpload()}
        {isPdfPackage && !videos && (
          <div>
            <div style={{ marginBottom: 15 }}>
              <p style={{ marginBottom: 10, fontWeight: 'bold' }}>Content Type:</p>
              <div style={{ display: 'flex', gap: 20, flexWrap: 'wrap' }}>
                <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                  <input
                    type="radio"
                    name="pdfContentType"
                    value="link"
                    checked={this.state.pdfContentType === "link"}
                    onChange={(e) => this.handlePdfContentTypeChange(e.target.value)}
                    style={{ marginRight: 8 }}
                  />
                  PDF Link
                </label>
                <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                  <input
                    type="radio"
                    name="pdfContentType"
                    value="notes"
                    checked={this.state.pdfContentType === "notes"}
                    onChange={(e) => this.handlePdfContentTypeChange(e.target.value)}
                    style={{ marginRight: 8 }}
                  />
                  Notes Text + PDF Upload
                </label>
              </div>
            </div>
            {this.state.pdfContentType === "link" ? (
              <TextField
                required
                className="input-box "
                label="PDF Link"
                variant="filled"
                style={{ width: "80%" }}
                value={description}
                onChange={(e) => this.setState({ description: e.target.value })}
                focused
              />
            ) : (
              // Notes Text + PDF Upload
              <div>
                  {/* PDF Upload Section */}
                <div style={{ marginTop: 30, borderTop: '1px solid #eee', paddingTop: 20 }}>
                  <p style={{ marginBottom: 10, fontWeight: 'bold' }}>Upload PDF File:</p>
                  {this.renderPdfUpload()}
                </div>
                <p style={{ marginBottom: 10, fontWeight: 'bold' }}>Notes Content:</p>
                <Editor
                  key={`pdf-notes-editor-${this.state.examid}-${this.state.pdfContentType}`}
                  initialValue={descriptionText || ''}
                  init={{
                    height: 300,
                    width: "80%",
                    menubar: false,
                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Ramabhadra; font-size:14px }",
                    setup: (editor) => {
                      editor.on('init', () => {
                         // Set cursor at the end of content
                        editor.selection.select(editor.getBody(), true);
                        editor.selection.collapse(false);
                      });
                    }
                  }}
                  onEditorChange={(e) => {
                     this.setState({ description: e });
                  }}
                />

              
              </div>
            )}
          </div>
        )}
        {isPdfPackage && videos && (
          <TextField
            required
            className="input-box "
            label="Video ID (32-character Cloudflare Stream ID)"
            variant="filled"
            style={{ width: "80%" }}
            value={description}
            onChange={(e) => this.setState({ description: e.target.value })}
            focused
            // helperText="This field will be automatically filled when you upload a video above"
            disabled={true}  // Make it read-only since it's auto-filled
          />
        )}
        <div>
          Start Date <br />
          <input
            type="text"
            id="startdate"
            className="input-box-add-exam"
            value={startdate}
            onChange={this.onChangeState}
          />
        </div>
        <div>
          Day <br />
          <input
            type="number"
            id="day"
            className="input-box-add-exam"
            value={day}
            onChange={this.onChangeState}
          />
        </div>
        <div>
          End Date <br />
          <input
            type="text"
            id="enddate"
            className="input-box-add-exam"
            value={enddate}
            onChange={this.onChangeState}
          />
        </div>
        <div className="input-container">
          <label style={{ display: "flex", alignItems: "center", gap: "10px" }}>
            <input
              type="checkbox"
              checked={this.state.isSinglePaidExam}
              onChange={(e) =>
                this.setState({ isSinglePaidExam: e.target.checked })
              }
            />
            Mark as Single Paid {isPdfPackage ? "PDF/Video" : "Exam"}
          </label>
        </div>

        {this.state.isSinglePaidExam && (
          <div className="input-container">
            <label htmlFor="examPrice">{isPdfPackage ? "PDF/Video" : "Exam"} Price (₹)</label>
            <input
              type="number"
              id="examPrice"
              value={this.state.examPrice}
              onChange={(e) => this.setState({ examPrice: e.target.value })}
              placeholder="Enter exam price"
            />
          </div>
        )}

        {/* { !isPdfPackage && ( */}
        <div>
          <p
            style={{
              fontSize: "16px",
              fontWeight: "500",
              marginBottom: "10px",
            }}
          >
            Assign to Groups
          </p>
          <div className="packages-container">
            {packages.map((e) => {
              const isNormalPackage = e.isNormalPackage === "1";
              const isVideosPackage = e.isNormalPackage === "3";
              const isPDFPackage = !isNormalPackage && !isVideosPackage;

              return (
                <div key={"addnewexams" + e.gid} style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  marginBottom: '10px',
                  padding: '8px',
                  borderRadius: '4px',
                  backgroundColor: isNormalPackage ? 'rgba(76, 175, 80, 0.1)' : 
                                  isVideosPackage ? 'rgba(33, 150, 243, 0.1)' : 
                                  'rgba(255, 87, 34, 0.1)',
                  border: `1px solid ${
                    isNormalPackage ? '#4CAF50' : 
                    isVideosPackage ? '#2196F3' : 
                    '#FF5722'
                  }`
                }}>
                  <input
                    type={"checkbox"}
                    name={"gids[]"}
                    value={e.gid}
                    id={"selectpackage" + e.gid}
                    checked={selectedGids.indexOf(String(e.gid)) > -1}
                    style={{ marginRight: 10 }}
                    onChange={(ev) => {
                      if (selectedGids.indexOf(String(ev.target.value)) === -1) {
                        const newgroup = selectedGids;
                        newgroup.push(String(ev.target.value));
                        this.setState({ selectedGids: newgroup });
                      } else {
                        const newgroup = selectedGids.filter(
                          (v) => v !== String(ev.target.value)
                        );
                        this.setState({ selectedGids: newgroup });
                      }
                    }}
                  />
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: '10px',
                    flex: 1
                  }}>
                    {isNormalPackage ? (
                      <QuizIcon style={{ color: '#4CAF50' }} />
                    ) : isVideosPackage ? (
                      <VideoLibraryIcon style={{ color: '#2196F3' }} />
                    ) : (
                      <PictureAsPdfIcon style={{ color: '#FF5722' }} />
                    )}
                    <label
                      htmlFor={"selectpackage" + e.gid}
                      style={{ 
                        cursor: "pointer",
                        color: isNormalPackage ? '#4CAF50' : 
                               isVideosPackage ? '#2196F3' : 
                               '#FF5722',
                        fontWeight: '500'
                      }}
                    >
                      {e.group_name}
                    </label>
                  </div>
                  <div style={{ 
                    fontSize: '12px',
                    color: isNormalPackage ? '#4CAF50' : 
                           isVideosPackage ? '#2196F3' : 
                           '#FF5722',
                    opacity: 0.8
                  }}>
                    {isNormalPackage ? 'Exam Package' : 
                     isVideosPackage ? 'Video Package' : 
                     'PDF Package'}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        {/* )} */}

        {!isPdfPackage && (
          <div>
            <p
              style={{
                color: "blue",
                cursor: "pointer",
                textDecoration: "underline",
              }}
              onClick={() =>
                this.setState((p) => ({
                  showAdvancedOptions: !p.showAdvancedOptions,
                }))
              }
            >
              Show Advanced Options
            </p>
            {showAdvancedOptions && this.renderAdvancedOptions()}
          </div>
        )}

        {!isPdfPackage && (
          <div>
            <Button
              className="btn navigate"
              onClick={examid !== 0 ? this.showQuestionAdd : this.createQuiz}
            >
              {examid !== 0 ? "Add Questions into Exam" : "Create Exam"}
            </Button>
          </div>
        )}
        {isPdfPackage && (
          <div>
            <Button className="btn navigate" onClick={this.createQuiz}>
              {examid !== 0 ? "Update PDF/Video" : "Add PDF/Video"}
            </Button>
          </div>
        )}
        {/* 
        {addType === "automatic" &&
          examid !== 0 &&
          this.renderAutomaticSelection()} */}
        {!isPdfPackage && examid !== 0 && (
          <>
            <div>
              <div style={{ display: "flex", justifyContent: "flex-end" }}>
                {/* <div
                  onClick={() =>
                    this.setState((p) => ({ showQuestions: !p.showQuestions }))
                  }
                  style={{ cursor: "pointer" }}
                >
                  {!showQuestions ? (
                    <i className="bi bi-eye-fill">{"Show Questions"}</i>
                  ) : (
                    <i className="bi bi-eye-slash"> {"Hide Questions"}</i>
                  )}
                </div> */}
                <div style={{ display: "flex", marginLeft: 20 }}>
                  No.of Questions added{" "}
                  <div
                    style={{
                      backgroundColor: "black",
                      borderRadius: 40,
                      width: 30,
                      height: 30,
                      color: "#FFF",
                      marginLeft: 5,
                      textAlign: "center",
                    }}
                  >
                    {selectedqids.length}
                  </div>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "center",
                      marginLeft: 15,
                    }}
                    onClick={() =>
                      this.setState({ selectedqids: [], selectedQData: [] })
                    }
                  >
                    <i
                      className="bi bi-trash-fill"
                      style={{ cursor: "pointer" }}
                    >
                      {" "}
                      Remove All Questions
                    </i>
                  </div>
                </div>
              </div>
              {examid !== 0 && !isLoading && this.renderSelectedQuestions()}
            </div>

            <div>
              <Button
                className="btn navigate"
                style={{ position: "absolute", top: 34, right: 46 }}
                onClick={this.createQuiz}
              >
                Update Exam
              </Button>
            </div>
          </>
        )}
      </div>
    );
  };

  getOptions = async () => {
    const { qNum, qReportData } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "getOptions",
      search: "",
      qid: qNum,
    };
    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      // console.log("data", data.data);
      const options = data.data[0];
      this.setState({
        editorLoading: false,
        // popupType: "Edit Question",
        op1: data.data[0].length >= 1 ? data.data[0][0].q_option : "",
        op2: data.data[0].length >= 2 ? data.data[0][1].q_option : "",
        op3: data.data[0].length >= 3 ? data.data[0][2].q_option : "",
        op4: data.data[0].length >= 4 ? data.data[0][3].q_option : "",
        op5: data.data[0].length >= 5 ? data.data[0][4].q_option : "",
        op1Num: data.data[0].length >= 1 ? data.data[0][0].oid : 0,
        op2Num: data.data[0].length >= 2 ? data.data[0][1].oid : 0,
        op3Num: data.data[0].length >= 3 ? data.data[0][2].oid : 0,
        op4Num: data.data[0].length >= 4 ? data.data[0][3].oid : 0,
        op5Num: data.data[0].length >= 5 ? data.data[0][4].oid : 0,
        correctOption: data.data[1][0].selectedOp,
        selectedCat: data.data[2][0].selectedCategory,
        questionText: data.data[2][0].question,
        optionsData: options,
        addOptionClicked: false,
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  questionEditor = () => {
    const {
      qNum,
      editorLoading,
      optionsData,
      categoryList,
      selectedCat,
      correctOption,
      popupType,
      addOptionClicked,
      errQdata,
    } = this.state;
    let { selectedQData } = this.state;
    const question = selectedQData.filter((e) => e.qid == qNum)[0];
    // this.setState({ selectedCat: question.cid });
    return (
      <>
        {(popupType === "Edit Question" ||
          popupType === "Edit Error Question") &&
        !editorLoading &&
        question !== undefined &&
        categoryList !== undefined ? (
          <>
            <div>
              Select Category{" "}
              <select
                onChange={(e) => this.setState({ selectedCat: e.target.value })}
              >
                {categoryList.map((category) => {
                  return (
                    <option
                      key={"category" + category.cid}
                      value={category.cid}
                      selected={
                        selectedCat === category.cid ||
                        question.cid === category.cid
                      }
                    >
                      {category.category_name}
                    </option>
                  );
                })}
              </select>
            </div>
            <div>
              Question
              <Editor
                key={`question-editor-${qNum}`}
                value={this.state.questionText}
                init={{
                  height: 200,
                  width: "100%",
                  menubar: false,

                  plugins: [
                    "advlist autolink lists link image charmap print preview anchor",
                    "searchreplace visualblocks code fullscreen",
                    "insertdatetime media table paste code help wordcount",
                  ],
                  toolbar:
                    "undo redo | formatselect | " +
                    "bold italic backcolor | alignleft aligncenter " +
                    "alignright | bullist numlist | subscript superscript |  " +
                    " code " +
                    "removeformat",
                  content_style:
                    "body { font-family:Ramabhadra; font-size:14px }",
                  setup: (editor) => {
                    editor.on('init', () => {
                      // Set cursor at the end of content
                      editor.selection.select(editor.getBody(), true);
                      editor.selection.collapse(false);
                    });
                  }
                }}
                onEditorChange={(e) => this.setState({ questionText: e })}
              />
            </div>

            {optionsData.map((e, i) => (
              <div style={{ marginTop: 15 }}>
                Option {i + 1}{" "}
                <input
                  type={"radio"}
                  value={e.oid}
                  name={qNum}
                  id={e.oid}
                  onChange={(e) =>
                    this.setState({ correctOption: e.target.value })
                  }
                  checked={correctOption == e.oid}
                />{" "}
                <label htmlFor={e.oid}>Correct option</label>
                {i >= 4 && (
                  <i
                    className="bi bi-trash-fill"
                    style={{ cursor: "pointer", marginLeft: 10 }}
                    onClick={() =>
                      this.setState(
                        {
                          oid: e.oid,
                          optionsData:
                            e.oid === 0
                              ? optionsData.slice(0, optionsData.length - 1)
                              : optionsData,
                          addOptionClicked: false,
                        },
                        () => {
                          if (e.oid !== 0) {
                            this.deleteOption();
                          }
                        }
                      )
                    }
                  ></i>
                )}
                <Editor
                  key={`option-editor-${e.oid}-${qNum}`}
                  value={this.state["op" + (i + 1)]}
                  init={{
                    height: 100,
                    width: "90%",
                    menubar: false,

                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                    setup: (editor) => {
                      editor.on('init', () => {
                        // Set cursor at the end of content
                        editor.selection.select(editor.getBody(), true);
                        editor.selection.collapse(false);
                      });
                    }
                  }}
                  onEditorChange={(value) =>
                    this.setState({
                      ["op" + (i + 1)]: value,
                      ["op" + (i + 1) + "Num"]: e.oid,
                    })
                  }
                />
              </div>
            ))}
            {/* {[...options, { oid: 0, q_option: "", score: 0 }]} */}

            {optionsData.length < 5 && (
              <div style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button
                  className="btn addbtn"
                  style={{ color: "white" }}
                  onClick={() =>
                    this.setState((p) => ({
                      optionsData: [
                        ...p.optionsData,
                        { oid: 0, q_option: "", score: 0 },
                      ],
                      addOptionClicked: true,
                    }))
                  }
                >
                  Add Option
                </Button>
              </div>
            )}
          </>
        ) : (
          <div>
            <Loader />
          </div>
        )}
      </>
    );
  };
  renderSelectedQuestions = () => {
    const { selectedQData, selectedqids, multipleSelectUsed, showQuestions } =
      this.state;
    const style = `
    table {
      font-family: arial, sans-serif;
      border-collapse: collapse;
      width: 100%;
    }
    
    td,
    th {
      border: 1px solid #dddddd;
      text-align: left;
      padding: 10px;
      height: "100%";
    }
    
    tr:nth-child(even) {
      background-color: #dddddd;
    }`;
    return (
      <table style={{ marginTop: 10 }}>
        <style>{style}</style>
        <thead>
          <tr>
            <th>Question</th>
            <th>Category</th>
            <th>EM</th>
            <th></th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          {/* {!showQuestions && (
            <tr onClick={() => this.setState({ showQuestions: true })}>
              <td colSpan={4}>View All Questions ....</td>
            </tr>
          )} */}
          {selectedQData.length > 0 &&
            !multipleSelectUsed &&
            showQuestions &&
            selectedQData.map((e, i) => {
              return (
                <tr key={"addedQuestions" + e.qid}>
                  <td>
                    <div style={{ display: "flex", height: "100%" }}>
                      {e.qid + ") "}
                      <span
                        style={{ marginLeft: 5 }}
                        dangerouslySetInnerHTML={{ __html: e.question }}
                      ></span>
                    </div>
                  </td>
                  <td>{e.category_name}</td>
                  <td>{e?.question_in_english ? "YES" : "NO"} </td>

                  <td>
                    <a
                      href={"/admin/qbank/view-all?qid=" + e.qid}
                      target="__blank"
                    >
                      <i
                        className="bi bi-pencil-fill"
                        style={{ cursor: "pointer", textAlign: "center" }}
                      ></i>
                    </a>
                  </td>
                  <td
                    style={{ cursor: "pointer" }}
                    onClick={(ev) => {
                      if (selectedqids.indexOf(String(e.qid)) === -1) {
                        const newgroup = selectedqids;
                        newgroup.push(String(e.qid));
                        this.setState({
                          selectedqids: newgroup,
                          duration: newgroup.length,
                        });
                      } else {
                        const newgroup = selectedqids.filter(
                          (v) => v !== String(e.qid)
                        );
                        const remainQuestions = selectedQData.filter(
                          (v2) => v2.qid !== e.qid
                        );

                        this.setState({
                          selectedqids: newgroup,
                          selectedQData: remainQuestions,
                          duration: newgroup.length,
                        });
                      }
                    }}
                  >
                    {" "}
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        height: "100%",
                      }}
                    >
                      <i
                        className={`bi ${
                          selectedqids.indexOf(String(e.qid)) === -1
                            ? "bi-plus-circle"
                            : "bi-trash-fill"
                        } `}
                      ></i>
                    </div>
                  </td>
                </tr>
              );
            })}
          {multipleSelectUsed && selectedqids.length >= 0 && (
            <tr>
              <td colSpan={4}>Please update the Exam to view Questions</td>
            </tr>
          )}
          {selectedqids.length === 0 && (
            <tr>
              <td colSpan={4}>No Questions are added..</td>
            </tr>
          )}
        </tbody>
      </table>
    );
  };
  saveQuestion = async () => {
    const {
      op1,
      op2,
      op3,
      op4,
      op5,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      selectedCat,
      correctOption,
      qNum,
      quizId,
      qReportData,
      op5Num,
    } = this.state;
    let { questionText } = this.state;

    // console.log(correctOption);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      questionText,
      op1,
      op2,
      op3,
      op4,
      op5,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      op5Num,
      selectedCat,
      correctOption: parseInt(correctOption),
      qNum,
      quizId,
      type: "EDITQuestion",
    };
    if (questionText === "") {
      NotificationManager.error(`Please Enter Question`);
    } else if (op1 === "") {
      NotificationManager.error(`Please Enter Option 1`);
    } else if (op2 === "") {
      NotificationManager.error(`Please Enter Option 2`);
    } else if (op3 === "") {
      NotificationManager.error(`Please Enter Option 3`);
    } else if (op4 === "") {
      NotificationManager.error(`Please Enter Option 4`);
    } else if (op1Num === 0 && op2Num === 0 && op3Num === 0 && op4Num === 0) {
      NotificationManager.error(`Please Select Correct Answer`);
    } else if (selectedCat === null) {
      NotificationManager.error(`Please Select Category`);
    } else {
      try {
        this.setState({ editorLoading: true });

        const data = await axios.post(
          `${commonData["api"]}/admin/add-edit-question`,
          body,
          { headers }
        );
        //   console.log(data);
        const newQuestion = {
          cid: selectedCat,
          question: questionText,
          questionid: qNum,
        };
        const index = qReportData.findIndex(
          (ek) => ek == qReportData.filter((e) => e.questionid === qNum)[0]
        );

        const removedData = qReportData.filter((e) => e.questionid !== qNum);
        const newQUestionsList = removedData.splice(index, 1, newQuestion);
        this.setState({
          editorLoading: false,
          popUpOpen: false,
          questionText: "",
          op1: "",
          op2: "",
          op3: "",
          op4: "",
          op5: "",
          op5Num: 0,
          op1Num: 0,
          op2Num: 0,
          op3Num: 0,
          op4Num: 0,
          correctOption: null,
          selectedCat: null,
          qReportData: removedData,
          addOptionClicked: false,
        });
        NotificationManager.success(`Question Updated Successfully..`);
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ editorLoading: false });
      }
    }
  };
  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      questionText: "",
      op1: "",
      op2: "",
      op3: "",
      op4: "",
      correctOption: 0,
      selectedCat: "",
      op5: "",
      addOptionClicked: false,
    }));
  };
  renderPopUp = () => {
    const { popUpOpen, popupType } = this.state;
    // console.log(bonus);
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"lg"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{popupType}</p>
            </div>
            <Button
              style={{ backgroundColor: "red", color: "#fff" }}
              onClick={this.handleOpen}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          {this.questionEditor()}
        </DialogContent>
        <DialogActions>
          <Button
            className="btn header-btns attemptbtn attempt-btns submit popbtn"
            onClick={this.handleOpen}
          >
            Cancel
          </Button>
          <Button
            className="btn header-btns attemptbtn attempt-btns popbtn"
            onClick={this.saveQuestion}
          >
            Save changes
          </Button>
        </DialogActions>
      </Dialog>
    );
  };
  createQuiz = async () => {
    const {
      name,
      startdate,
      duration,
      attempts,
      selectedGids,
      description,
      selectedqids,
      type,
      examid,
      enddate,
      minPercent,
      open,
      addType,
      categoryString,
      isPdfPackage,
      videos,
      day,
      isSinglePaidExam,
      examPrice,
      pdfContentType,
    } = this.state;
    // const { name, description, duration, selectedGids } = this.state;
    if (name === "") {
      NotificationManager.error(
        `Please Enter ${isPdfPackage ? "Pdf/Video" : "Exam"} Name`
      );
    } else if (description === "") {
      let errorMessage = "Description";
      if (isPdfPackage) {
        if (this.state.pdfContentType === "link") {
          errorMessage = "PDF Link";
        } else if (this.state.pdfContentType === "notes") {
          errorMessage = "Notes Content";
        }
      }
      NotificationManager.error(`Please Enter ${errorMessage}`);
    }
    // else if (duration === "" || duration === "0") {
    //   NotificationManager.error(`Please Enter Duration`);
    // }
    else if (!isSinglePaidExam && selectedGids.length === 0) {
      NotificationManager.error(`Please select atleast one Package`);
    } else {
      // Process description based on content type
      let processedDescription = description;
      if (isPdfPackage && !videos && pdfContentType === "notes" && description) {
        // Encrypt notes content before sending to API
        processedDescription = this.encryptContent(description);
      }

      // console.log(selectedqids);
      const token = Cookie.get("jwt_token");
      const headers = {
        "Content-Type": "application/json",
        authorization: token,
        "Access-Control-Allow-Origin": "*",
      };

      const body = {
        type: type,
        examid,
        name,
        startdate,

        attempts,
        selectedGids:
          selectedGids.join(",") +
          "&&$" +
          open +
          "&&$" +
          (addType !== "manual" ? "1" : "0") +
          "&&$" +
          categoryString +
          "&&$" +
          day +
          "&&$" +
          (isSinglePaidExam ? 1 : 0) +
          "&&$" +
          (isSinglePaidExam ? examPrice : 0),
        description: processedDescription,
        selectedqids: selectedqids.join(","),
        noq: selectedqids.length,
        enddate,
        minPercent,
        duration: videos ? "3" : isPdfPackage ? "2" : "1",
      };
      // console.log(body);
      try {
        this.setState({ isLoading: true });

        const data = await axios.post(
          `${commonData["api"]}/admin/add-edit-exam`,
          body,
          { headers }
        );
        // console.log(data);
        if (type === "Add") {
          const quid = data.data[0][0].quid;
          const { history } = this.props;
          this.setState({
            isLoading: false,
            examid: quid,
          });
          const { location } = this.props;
          const { search } = location;
          history.replace(`/admin/exams/add-edit-exam/${quid}${search.trim()}`);
          this.loadData();
          this.setState({ showQuestionAddPage: isPdfPackage ? false : true });
          this.getData();
          const element = description.replace(/<[^>]+>/g, "");
          // await sendpush({
          //   title: `New Exam Updated...`,
          //   message: `Exam Name: ${name} \nSyllabus: ${element} `,
          //   // filters: [
          //   //   { field: "tag", key: "number", relation: "=", value: String(uid) },
          //   // ],
          //   url: `/notifications`,
          //   web_buttons: [
          //     {
          //       id: "like-button",
          //       text: "➤ Know More",
          //       url: `/`,
          //     },
          //   ],
          //   send_after: new Date(startdate),
          // });
        } else {
          this.examsDetails();
          this.setState({
            isLoading: false,
            multipleSelectUsed: false,
          });
        }
        NotificationManager.success(
          `${isPdfPackage ? "Pdf/Video" : "Exam"} Successfully ${
            type === "Edit" ? "Updated" : "Created"
          } `
        );
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ isLoading: false });
      }
    }
  };
  getQUestionsCount = (catgoryId, val) => {
    const { selectedCidSelectedValue } = this.state;
    const questions = [];
    for (let i = 1; i <= val; i++) {
      questions.push(i);
    }
    // console.log(questions);
    return (
      <select
        style={{
          width: "230px",
          border: "2px solid orange",
          padding: 5,
          cursor: "pointer",
          marginBottom: 10,
        }}
        value={selectedCidSelectedValue}
        onChange={(e) =>
          this.setState({ selectedCidSelectedValue: parseInt(e.target.value) })
        }
      >
        {" "}
        <option value={0}>{"Select no. of questions"}</option>
        {questions.map((valll) => (
          <option value={valll} selected={catgoryId == valll}>
            {valll}
          </option>
        ))}
      </select>
    );
  };

  addAutomaticQuestions = () => {
    const { selectedCidSelectedValue } = this.state;

    if (selectedCidSelectedValue === 0) {
      NotificationManager.error(`Please select atleast 1 question`);
    } else {
      this.setState({ multipleSelectUsed: false }, () =>
        this.getQuestionNums(selectedCidSelectedValue)
      );
    }
  };
  renderAutomaticSelection = () => {
    const { categoryList, selectedCid, selectedCidMaxCount, categoryString } =
      this.state;
    return (
      <div className="automaticselction">
        {categoryString !== "" &&
          categoryString.split(",").length > 0 &&
          categoryString.split(",").map((w) => (
            <div
              style={{
                display: "flex",
                flexWrap: "wrap",
              }}
            >
              <div>
                Select Category :{" "}
                <select
                  style={{
                    width: "230px",
                    border: "2px solid orange",
                    padding: 5,
                    cursor: "pointer",
                    marginBottom: 10,
                  }}
                  onChange={(e) =>
                    this.setState({
                      selectedCid: e.target.value.split("%%")[0],
                      selectedCidMaxCount: e.target.value.split("%%")[1],
                    })
                  }
                >
                  <option value={0}>{"Select Category Name"}</option>
                  {categoryList.map((eachCateg) => (
                    <option
                      value={eachCateg.cid + "%%" + eachCateg.count}
                      selected={parseInt(w.split("_")[0]) === eachCateg.cid}
                    >
                      {eachCateg.category_name}
                    </option>
                  ))}
                </select>
              </div>
              <div style={{ marginLeft: 15, display: "flex" }}>
                Select no. of questions :{" "}
                <div
                  style={{
                    backgroundColor: "black",
                    borderRadius: 40,
                    width: 30,
                    height: 30,
                    color: "#FFF",
                    marginLeft: 5,
                    textAlign: "center",
                  }}
                >
                  {parseInt(w.split("_")[1])}
                </div>
              </div>{" "}
            </div>
          ))}
        {categoryString !== "" && (
          <div style={{ display: "flex", flexDirection: "column" }}>
            <hr style={{ color: "black" }} />
            {categoryString.split(",").length > 0 && (
              <h4 style={{ marginBottom: 10 }}>Add More Questions</h4>
            )}
          </div>
        )}
        <div>
          Select Category :{" "}
          <select
            style={{
              width: "230px",
              border: "2px solid orange",
              padding: 5,
              cursor: "pointer",
              marginBottom: 10,
            }}
            onChange={(e) =>
              this.setState({
                selectedCid: e.target.value.split("%%")[0],
                selectedCidMaxCount: e.target.value.split("%%")[1],
              })
            }
          >
            <option value={0}>{"Select Category Name"}</option>
            {categoryList.map((eachCateg) => (
              <option value={eachCateg.cid + "%%" + eachCateg.count}>
                {eachCateg.category_name}
              </option>
            ))}
          </select>
        </div>
        {selectedCid !== "" && (
          <div>
            Selected no. of questions :{" "}
            {this.getQUestionsCount("", selectedCidMaxCount)}
          </div>
        )}
        <div>
          <Button className="btn navigate" onClick={this.addAutomaticQuestions}>
            Add Questions
          </Button>
        </div>
      </div>
    );
  };

  // Handle video upload completion
  handleVideoUploaded = (videoId) => {
    this.setState({ 
      cloudflareVideoId: videoId,
      description: videoId  // Save video ID as description immediately
    });
  };

  // Handle video details loaded
  handleVideoDetailsLoaded = (videoDetails) => {
    // Save the video ID as description instead of the full URL
    if (videoDetails && videoDetails.uid) {
      this.setState({ 
        cloudflareVideoUrl: videoDetails.playback?.hls || null,
        description: videoDetails.uid  // Save video ID instead of URL
      });
    }
  };

  // Handle video deleted
  handleVideoDeleted = () => {
    this.setState({
      cloudflareVideoId: null,
      cloudflareVideoUrl: null,
      description: ''
    });
  };

  renderPdfUpload = () => {
    const {
      uploadedPdfFile,
      uploadedPdfUrl,
      isUploadingPdf,
      uploadProgress,
      existingPdfFile,
      isCheckingPdf,
      isDeletingPdf
    } = this.state;

    return (
      <div style={{ marginTop: 20 }}>
        {/* Existing PDF File Section */}
        {isCheckingPdf && (
          <div style={{
            backgroundColor: '#f8f9fa',
            padding: 15,
            borderRadius: 8,
            border: '1px solid #dee2e6',
            marginBottom: 15,
            textAlign: 'center'
          }}>
            <p>🔍 Checking for existing PDF file...</p>
          </div>
        )}

        {existingPdfFile && (
          <div style={{
            backgroundColor: '#e8f5e8',
            padding: 15,
            borderRadius: 8,
            border: '2px solid #4CAF50',
            marginBottom: 15
          }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}>
              <span style={{ fontSize: '20px', marginRight: 10 }}>📄</span>
              <p style={{ marginBottom: 0, fontWeight: 'bold', color: '#2e7d32', flexGrow: 1 }}>
                Existing PDF File Found
              </p>
            </div>

            <div style={{ marginBottom: 15 }}>
              <div style={{ fontSize: '14px', marginBottom: 5 }}>
                <strong>File:</strong> {existingPdfFile.originalName}
              </div>
              <div style={{ fontSize: '14px', marginBottom: 5 }}>
                <strong>Size:</strong> {this.formatFileSize(existingPdfFile.fileSize)}
              </div>
              <div style={{ fontSize: '14px', marginBottom: 5 }}>
                <strong>Uploaded:</strong> {this.formatDate(existingPdfFile.uploadDate)}
              </div>
              <div style={{ fontSize: '14px', marginBottom: 10 }}>
                <strong>Uploaded by:</strong> {existingPdfFile.uploadedBy}
              </div>
            </div>

            <div style={{ display: 'flex', gap: 10, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                color="primary"
                onClick={this.handleDownloadExistingPdf}
                style={{ backgroundColor: '#1976d2' }}
              >
                � Preview/Download
              </Button>
              <Button
                variant="contained"
                color="error"
                onClick={this.handleDeleteExistingPdf}
                disabled={isDeletingPdf}
                style={{ backgroundColor: '#d32f2f' }}
              >
                {isDeletingPdf ? '🗑️ Deleting...' : '🗑️ Delete File'}
              </Button>
            </div>
          </div>
        )}

        <div style={{
          backgroundColor: '#f8f9fa',
          padding: 15,
          borderRadius: 8,
          border: '1px solid #dee2e6',
          marginBottom: 15
        }}>
          <p style={{ marginBottom: 10, fontWeight: 'bold', color: '#495057' }}>
            📎 {existingPdfFile ? 'Replace PDF File' : 'Upload PDF File'}
          </p>
          <p style={{ fontSize: '14px', color: '#6c757d', marginBottom: 15 }}>
            {existingPdfFile
              ? 'Upload a new PDF file to replace the existing one. The old file will be automatically deleted.'
              : 'Upload a PDF file that will be stored separately and accessible to users. This is independent of the notes content above.'
            }
          </p>

        {/* File Input */}
        <div style={{ marginBottom: 15 }}>
          <input
            type="file"
            accept=".pdf"
            onChange={this.handlePdfFileSelect}
            style={{ marginBottom: 10 }}
            disabled={isUploadingPdf}
          />
          {uploadedPdfFile && (
            <div style={{ fontSize: '14px', color: '#666', marginTop: 5 }}>
              Selected: {uploadedPdfFile.name} ({(uploadedPdfFile.size / 1024 / 1024).toFixed(2)} MB)
            </div>
          )}
        </div>

        {/* Upload Progress */}
        {isUploadingPdf && (
          <div style={{ marginBottom: 15 }}>
            <div style={{ marginBottom: 5 }}>Uploading... {uploadProgress}%</div>
            <div style={{
              width: '80%',
              height: '20px',
              backgroundColor: '#f0f0f0',
              borderRadius: '10px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${uploadProgress}%`,
                height: '100%',
                backgroundColor: '#4CAF50',
                transition: 'width 0.3s ease'
              }}></div>
            </div>
          </div>
        )}

        {/* Upload Button */}
        {uploadedPdfFile && !isUploadingPdf && !uploadedPdfUrl && (
          <Button
            variant="contained"
            onClick={this.handlePdfUpload}
            style={{
              backgroundColor: '#FF5722',
              color: 'white',
              marginBottom: 15
            }}
          >
            Upload PDF to Cloud
          </Button>
        )}

        {/* Success Message and Link */}
        {uploadedPdfUrl && (
          <div style={{
            marginBottom: 15,
            padding: 15,
            backgroundColor: '#e8f5e8',
            border: '1px solid #4CAF50',
            borderRadius: 5
          }}>
            <div style={{ color: '#4CAF50', fontWeight: 'bold', marginBottom: 10 }}>
              ✓ PDF uploaded successfully!
            </div>
            <div style={{ fontSize: '14px', marginBottom: 10 }}>
              <strong>File:</strong> {uploadedPdfFile?.name}
            </div>
            <div style={{ fontSize: '14px', marginBottom: 10 }}>
              <strong>Size:</strong> {uploadedPdfFile ? (uploadedPdfFile.size / 1024 / 1024).toFixed(2) : 0} MB
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              The PDF has been securely uploaded and will be accessible to users through the exam.
            </div>
          </div>
        )}

        {/* Clear/Reset Button */}
        {(uploadedPdfFile || uploadedPdfUrl) && !isUploadingPdf && (
          <Button
            variant="outlined"
            onClick={this.handlePdfReset}
            style={{
              borderColor: '#f44336',
              color: '#f44336',
              marginLeft: 10
            }}
          >
            Clear PDF
          </Button>
        )}
        </div>
      </div>
    );
  };

  renderVideoUpload = () => {
    const { cloudflareVideoId } = this.state;

    return (
      <CloudflareStreamManager
        existingVideoId={cloudflareVideoId}
        onVideoUploaded={this.handleVideoUploaded}
        onVideoDetailsLoaded={this.handleVideoDetailsLoaded}
        onVideoDeleted={this.handleVideoDeleted}
      />
    );
  };

  render() {
    const { isLoading, login, showQuestionAddPage } = this.state;
    return (
      <>
        {!isLoading && login === "valid" && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetailsAdmin">
                <AdminMenu />
              </div>
              <Header />
              {this.renderPopUp()}
              <Divider color="white" />

              <div className="viewresultsdesktop admin">
                {!showQuestionAddPage
                  ? this.addNewExamFunc()
                  : this.addQuestionPage()}
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && login === "invalid" && (
          <div className="not-found-div">
            <img
              src={invalid}
              className="not-found-img"
              alt="not-found-image"
            />
            <Link to="/" className="linkto">
              <Button
                variant="contained"
                className="btn"
                style={{ marginTop: 20 }}
              >
                Go to HomePage
              </Button>
            </Link>
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }

  checkForExistingVideo = () => {
    const { description, videos } = this.state;
    
    if (videos && description) {
      // Check if description is already a video ID (32-character hex string)
      const videoIdRegex = /^[a-f0-9]{32}$/i;
      if (videoIdRegex.test(description.trim())) {
        // console.log('Found existing video ID:', description.trim());
        this.setState({
          cloudflareVideoId: description.trim()
        });
        return description.trim();
      }
      
      // Try to extract video ID from existing URL if it's a full URL
      const cloudflarePatterns = [
        /customer-[a-z0-9]+\.cloudflarestream\.com\/([a-f0-9]{32})/i,  // HLS/DASH URLs
        /cloudflarestream\.com\/([a-f0-9]{32})/i,                      // Generic stream URLs
        /stream\/([a-f0-9]{32})/i,                                     // Stream API URLs
      ];

      for (const pattern of cloudflarePatterns) {
        const match = description.match(pattern);
        if (match && match[1]) {
          const videoId = match[1];
          // console.log('Extracted video ID from URL:', videoId);
          
          // Update description to just the video ID
          this.setState({
            cloudflareVideoId: videoId,
            description: videoId  // Replace URL with just the ID
          });
          
          return videoId;
        }
      }
    }
    
    // Reset if no video found
    this.setState({
      cloudflareVideoId: null
    });
    
    return null;
  };
}

export default AddNewExam;
