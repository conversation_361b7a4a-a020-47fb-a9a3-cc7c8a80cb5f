# Google Drive OAuth Setup Guide

## Overview
I've updated your code to use OAuth delegation instead of service accounts. This will allow uploads to your personal Google Drive account, solving the storage quota issue.

## Step 1: Create OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project (or create a new one)
3. Go to "APIs & Services" > "Credentials"
4. Click "Create Credentials" > "OAuth 2.0 Client IDs"
5. Choose "Web application"
6. Set these redirect URIs:
   - `http://localhost:3000/auth/google/callback` (for local testing)
   - `https://yourdomain.com/auth/google/callback` (for production)
7. Save and note down your **Client ID** and **Client Secret**

## Step 2: Enable Google Drive API

1. In Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Google Drive API"
3. Click on it and press "Enable"

## Step 3: Set Environment Variables

Create a `.env` file in your project root or set these environment variables:

```bash
GOOGLE_CLIENT_ID=your-client-id.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-client-secret
GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback
```

## Step 4: Get Your Refresh Token

1. Start your server
2. Visit: `http://localhost:3000/auth/google`
3. You'll be redirected to Google's consent screen
4. Sign in with your Google account
5. Grant permission to access your Drive
6. You'll be redirected back with your refresh token displayed
7. Copy the refresh token and add it to your environment variables:

```bash
GOOGLE_REFRESH_TOKEN=your-refresh-token-here
```

## Step 5: Update Your Environment

Add the refresh token to your `.env` file:

```bash
GOOGLE_CLIENT_ID=your-client-id.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-client-secret
GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback
GOOGLE_REFRESH_TOKEN=your-refresh-token-here
```

## Step 6: Restart Your Server

Restart your Node.js server to load the new environment variables.

## Step 7: Test Upload

Try uploading a PDF again. It should now work and upload to your personal Google Drive account.

## How It Works

1. **Initial Setup**: You authenticate once with your Google account
2. **Token Storage**: Your refresh token is stored in environment variables
3. **Automatic Refresh**: The system automatically gets new access tokens as needed
4. **User Uploads**: All PDF uploads go to your personal Google Drive account
5. **No Storage Limits**: Uses your personal Drive storage quota

## Troubleshooting

### "No refresh token available" error
- Make sure you've completed the OAuth setup (Step 4)
- Verify the `GOOGLE_REFRESH_TOKEN` environment variable is set
- Restart your server after setting environment variables

### "Invalid client" error
- Check your `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET`
- Verify the redirect URI matches what you set in Google Cloud Console

### "Access denied" error
- Make sure Google Drive API is enabled in your project
- Check that you granted the necessary permissions during OAuth setup

## Security Notes

- Keep your client secret and refresh token secure
- Don't commit these credentials to version control
- Use environment variables or secure configuration management
- Consider rotating credentials periodically

## Production Deployment

For production:
1. Update `GOOGLE_REDIRECT_URI` to your production domain
2. Add the production redirect URI to your OAuth client in Google Cloud Console
3. Set all environment variables on your production server
4. Complete the OAuth flow once on production to get the refresh token

## File Storage

- Files are uploaded to the root folder of your Google Drive
- You can organize them into folders manually if needed
- All uploaded files will be publicly accessible (as configured)
- Files are automatically deleted after download (as per your existing logic)

The system is now ready to use OAuth delegation for Google Drive uploads!
