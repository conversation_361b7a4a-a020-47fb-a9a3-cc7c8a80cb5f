# Google Drive API Replacement Summary

## Overview
This document summarizes the changes made to replace the Google APIs package with direct Google Drive REST API calls for Drive operations only, while keeping other Google APIs (like Sheets) unchanged.

## Changes Made

### 1. New Helper Functions Added
Four new helper functions were added to `src/backend.js` to handle Google Drive operations using direct REST API calls:

#### `getDriveAccessToken()`
- **Purpose**: Authenticates with Google Drive API using service account credentials
- **Method**: Creates JWT token and exchanges it for OAuth2 access token
- **Endpoint**: `https://oauth2.googleapis.com/token`
- **Returns**: Access token for Drive API calls

#### `uploadFileToDrive(accessToken, fileName, pdfBuffer)`
- **Purpose**: Uploads a PDF file to Google Drive
- **Method**: Uses multipart form data to upload file via REST API
- **Endpoint**: `https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart`
- **Returns**: File metadata including ID, name, and view links

#### `setDriveFilePermissions(accessToken, fileId)`
- **Purpose**: Makes uploaded file publicly readable
- **Method**: Sets file permissions via REST API
- **Endpoint**: `https://www.googleapis.com/drive/v3/files/{fileId}/permissions`
- **Returns**: Permission metadata

#### `deleteFileFromDrive(accessToken, fileId)`
- **Purpose**: Deletes a file from Google Drive
- **Method**: Sends DELETE request to Drive API
- **Endpoint**: `https://www.googleapis.com/drive/v3/files/{fileId}`
- **Returns**: Nothing (void)

### 2. Updated Endpoints

#### `/upload-pdf-to-drive` (POST)
**Before**: Used `google.auth.GoogleAuth` and `google.drive()` from googleapis package
**After**: Uses direct REST API calls through helper functions

**Changes**:
- Replaced `google.auth.GoogleAuth` with `getDriveAccessToken()`
- Replaced `drive.files.create()` with `uploadFileToDrive()`
- Replaced `drive.permissions.create()` with `setDriveFilePermissions()`
- Simplified error handling and response structure

#### `/delete-drive-pdf/:fileId` (DELETE)
**Before**: Used `google.auth.GoogleAuth` and `google.drive()` from googleapis package
**After**: Uses direct REST API calls through helper functions

**Changes**:
- Replaced `google.auth.GoogleAuth` with `getDriveAccessToken()`
- Replaced `drive.files.delete()` with `deleteFileFromDrive()`
- Simplified error handling

### 3. What Remained Unchanged

#### Google Sheets API Usage
- The `storeINExcel()` function still uses the googleapis package for Google Sheets operations
- Import statement `const { google } = require("googleapis");` is kept for Sheets API
- All Sheets-related functionality remains intact

#### Authentication Files
- The `google.json` service account file is still used for authentication
- Same scopes and permissions are maintained

## Technical Details

### Authentication Flow
1. Read service account credentials from `google.json`
2. Create JWT token with appropriate claims
3. Sign JWT with private key from service account
4. Exchange JWT for OAuth2 access token
5. Use access token for subsequent API calls

### File Upload Process
1. Get access token using service account
2. Create multipart form data with file metadata and binary content
3. Upload file to Google Drive using REST API
4. Set public read permissions on uploaded file
5. Return file information including download links

### Error Handling
- Proper error handling for authentication failures
- Graceful handling of file not found errors during deletion
- Detailed error logging for debugging

## Benefits of This Approach

1. **Reduced Dependencies**: No longer dependent on the full googleapis package for Drive operations
2. **Direct Control**: Full control over HTTP requests and responses
3. **Better Performance**: Lighter weight implementation without googleapis overhead
4. **Flexibility**: Easier to customize and extend functionality
5. **Transparency**: Clear understanding of what API calls are being made

## Testing Recommendations

1. Test file upload functionality with various PDF sizes
2. Verify file permissions are set correctly (public read access)
3. Test file deletion functionality
4. Ensure error handling works for various failure scenarios
5. Verify that Google Sheets functionality remains unaffected

## Dependencies

The implementation uses existing dependencies:
- `axios` for HTTP requests
- `jsonwebtoken` for JWT creation
- `fs` for reading service account file
- Standard Node.js modules

No new dependencies were added.
