# Enhanced PDF Upload & Secure Retrieval System

This document provides a comprehensive guide to the enhanced PDF upload system with secure file retrieval capabilities.

## 🎯 System Overview

The enhanced system provides:
1. **Secure PDF Upload** with quiz association and database storage
2. **Temporary Signed URLs** for secure file access (15-minute expiration)
3. **Access Control** with user permission validation
4. **Rate Limiting** to prevent abuse
5. **Comprehensive Logging** for security monitoring

## 🔄 Workflow

### Upload Process
1. User authenticates with JWT token
2. User provides PDF file and quiz ID
3. System validates file type and size
4. File is uploaded to Cloudflare R2
5. File metadata is stored in database
6. Upload is logged for audit trail

### Retrieval Process
1. User requests file access with quiz ID
2. System validates user permissions
3. Database is queried for file information
4. Temporary signed URL is generated (15-min expiry)
5. Access attempt is logged
6. Secure URL is returned to user

## 🛡️ Security Architecture

### Authentication & Authorization
- **JWT Authentication**: Required for all operations
- **User Permissions**: Only file owners or admins can access files
- **Admin Users**: Configured in backend (phone numbers: 9640717460, 7396019228)

### Rate Limiting
- **Upload Limit**: 5 uploads per 15 minutes per user
- **Access Limit**: 20 retrievals per 15 minutes per user
- **Implementation**: In-memory store (use Redis in production)

### URL Security
- **Temporary URLs**: Expire after 15 minutes
- **Signed URLs**: Use AWS signature v4 for authentication
- **No Direct Access**: Public URLs are not provided in responses

### Access Logging
All operations are logged with:
- User phone number
- Quiz ID
- File name
- Action type (upload/access)
- Success/failure status
- Error details (if any)
- Timestamp

## 📊 Database Schema

### quiz_files Table
```sql
CREATE TABLE quiz_files (
  id INT AUTO_INCREMENT PRIMARY KEY,
  quiz_id VARCHAR(255) NOT NULL,           -- Quiz identifier
  file_name VARCHAR(500) NOT NULL,         -- Generated unique filename
  original_name VARCHAR(500) NOT NULL,     -- Original uploaded filename
  r2_key VARCHAR(500) NOT NULL,           -- R2 storage key/path
  file_size BIGINT NOT NULL,              -- File size in bytes
  uploaded_by VARCHAR(50) NOT NULL,       -- User phone from JWT
  upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  file_url TEXT,                          -- R2 public URL (reference only)
  
  -- Indexes for performance
  INDEX idx_quiz_id (quiz_id),
  INDEX idx_uploaded_by (uploaded_by),
  INDEX idx_upload_date (upload_date)
);
```

## 🔧 API Reference

### Upload Endpoint
**POST** `/upload-pdf-to-r2`

**Headers:**
- `Authorization: Bearer <jwt-token>`
- `Content-Type: multipart/form-data`

**Body:**
- `pdf` (file): PDF file (max 50MB)
- `quizId` (string): Quiz identifier

**Response:**
```json
{
  "success": true,
  "message": "PDF uploaded successfully to Cloudflare R2",
  "data": {
    "fileId": 123,
    "quizId": "quiz_001",
    "fileName": "pdfs/1703123456789_abc123_document.pdf",
    "originalName": "document.pdf",
    "size": 1048576,
    "r2Key": "pdfs/1703123456789_abc123_document.pdf",
    "uploadedAt": "2023-12-21T10:30:45.123Z",
    "uploadedBy": "**********",
    "accessNote": "Use /get-quiz-pdf/quiz_001 to get secure access URL"
  }
}
```

### Retrieval Endpoint
**GET** `/get-quiz-pdf/:quizId`

**Headers:**
- `Authorization: Bearer <jwt-token>`

**Response:**
```json
{
  "success": true,
  "message": "Secure access URL generated successfully",
  "data": {
    "fileId": 123,
    "quizId": "quiz_001",
    "fileName": "pdfs/1703123456789_abc123_document.pdf",
    "originalName": "document.pdf",
    "fileSize": 1048576,
    "uploadedBy": "**********",
    "uploadDate": "2023-12-21T10:30:45.000Z",
    "secureUrl": "https://account.r2.cloudflarestorage.com/bucket/file.pdf?X-Amz-...",
    "expiresIn": "15 minutes",
    "expiresAt": "2023-12-21T10:45:45.123Z"
  }
}
```

## 🚨 Error Handling

### Upload Errors
- **400**: Missing file, invalid file type, missing quiz ID
- **429**: Rate limit exceeded
- **500**: R2 upload error, database error

### Retrieval Errors
- **400**: Missing quiz ID
- **403**: Access denied (not owner or admin)
- **404**: File not found in database or R2
- **429**: Rate limit exceeded
- **500**: Database error, R2 error

### Common Error Responses
```json
{
  "success": false,
  "error": "Error description",
  "details": "Additional error details"
}
```

## 🔍 Monitoring & Logging

### Access Logs
Monitor the console output for access logs:
```json
{
  "timestamp": "2023-12-21T10:30:45.123Z",
  "user_phone": "**********",
  "quiz_id": "quiz_001",
  "file_name": "document.pdf",
  "action": "upload",
  "success": true,
  "error": null,
  "ip_address": "server-side"
}
```

### Rate Limiting Monitoring
- Monitor rate limit violations in logs
- Consider implementing alerts for excessive requests
- Use Redis in production for distributed rate limiting

## 🚀 Production Considerations

### Performance
- **Database Indexing**: Indexes on quiz_id, uploaded_by, upload_date
- **Connection Pooling**: MySQL connection pooling configured
- **File Size Limits**: 50MB limit to prevent abuse

### Scalability
- **Rate Limiting**: Use Redis for distributed systems
- **Database**: Consider read replicas for high traffic
- **R2 Storage**: Cloudflare R2 scales automatically

### Security
- **Environment Variables**: Store credentials securely
- **HTTPS**: Use HTTPS in production
- **JWT Validation**: Ensure JWT tokens are properly validated
- **Admin Access**: Regularly review admin user list

### Monitoring
- **Access Logs**: Implement centralized logging
- **Error Tracking**: Monitor error rates and types
- **Performance**: Track upload/retrieval response times
- **Storage**: Monitor R2 usage and costs

## 🧪 Testing

Use the enhanced HTML example at:
`http://localhost:4002/pdf-upload-example.html`

### Test Scenarios
1. **Upload PDF**: Test with valid JWT token and quiz ID
2. **Retrieve URL**: Test secure URL generation
3. **Access Control**: Test with different users
4. **Rate Limiting**: Test with multiple rapid requests
5. **Error Handling**: Test with invalid inputs

### Security Testing
- Test with expired JWT tokens
- Test access to other users' files
- Test rate limiting thresholds
- Test with malicious file types
- Test with oversized files

This enhanced system provides a secure, scalable solution for PDF file management with comprehensive access control and monitoring capabilities.
