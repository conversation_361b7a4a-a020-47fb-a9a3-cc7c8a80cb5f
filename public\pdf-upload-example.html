<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Upload to Cloudflare R2 - Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background-color: #28a745;
            width: 0%;
            transition: width 0.3s;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .file-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .auth-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PDF Upload to Cloudflare R2</h1>
        <p>This example demonstrates how to upload PDF files to Cloudflare R2 using the new API endpoint.</p>
        
        <div class="auth-section">
            <h3>Authentication Required</h3>
            <p>You need a valid JWT token to upload files. Please login first to get your token.</p>
            <input type="text" id="jwtToken" placeholder="Enter your JWT token here" style="width: 100%; padding: 8px; margin: 5px 0;">
            <input type="text" id="quizId" placeholder="Enter Quiz ID" style="width: 100%; padding: 8px; margin: 5px 0;">
        </div>

        <div class="upload-area" id="uploadArea">
            <h3>Select or Drop PDF File</h3>
            <p>Only PDF files are allowed (max 50MB)</p>
            <input type="file" id="pdfFile" accept=".pdf" />
            <div class="file-info" id="fileInfo" style="display: none;"></div>
        </div>

        <div class="progress" id="progressContainer">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <button id="uploadBtn" onclick="uploadPDF()" disabled>Upload PDF</button>
        <button id="getSecureUrlBtn" onclick="getSecureUrl()" disabled>Get Secure URL</button>
        <button onclick="clearAll()">Clear</button>

        <div class="result" id="result"></div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('pdfFile');
        const fileInfo = document.getElementById('fileInfo');
        const uploadBtn = document.getElementById('uploadBtn');
        const getSecureUrlBtn = document.getElementById('getSecureUrlBtn');
        const jwtToken = document.getElementById('jwtToken');
        const quizId = document.getElementById('quizId');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const result = document.getElementById('result');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        jwtToken.addEventListener('input', checkUploadReady);
        quizId.addEventListener('input', checkUploadReady);

        function handleFileSelect(file) {
            if (file.type !== 'application/pdf') {
                showResult('error', 'Please select a PDF file only.');
                return;
            }

            if (file.size > 50 * 1024 * 1024) {
                showResult('error', 'File size must be less than 50MB.');
                return;
            }

            fileInfo.innerHTML = `
                <strong>Selected File:</strong><br>
                Name: ${file.name}<br>
                Size: ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                Type: ${file.type}
            `;
            fileInfo.style.display = 'block';
            checkUploadReady();
        }

        function checkUploadReady() {
            const hasFile = fileInput.files.length > 0;
            const hasToken = jwtToken.value.trim() !== '';
            const hasQuizId = quizId.value.trim() !== '';
            uploadBtn.disabled = !(hasFile && hasToken && hasQuizId);
            getSecureUrlBtn.disabled = !(hasToken && hasQuizId);
        }

        async function uploadPDF() {
            const file = fileInput.files[0];
            const token = jwtToken.value.trim();
            const quiz = quizId.value.trim();

            if (!file || !token || !quiz) {
                showResult('error', 'Please select a PDF file, enter your JWT token, and Quiz ID.');
                return;
            }

            const formData = new FormData();
            formData.append('pdf', file);
            formData.append('quizId', quiz);

            uploadBtn.disabled = true;
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';

            try {
                const response = await fetch('/upload-pdf-to-r2', {
                    method: 'POST',
                    headers: {
                        'Authorization': token
                    },
                    body: formData
                });

                progressBar.style.width = '100%';

                const data = await response.json();

                if (data.success) {
                    showResult('success', `
                        <strong>Upload Successful!</strong><br>
                        File ID: ${data.data.fileId}<br>
                        Quiz ID: ${data.data.quizId}<br>
                        File: ${data.data.originalName}<br>
                        Size: ${(data.data.size / 1024 / 1024).toFixed(2)} MB<br>
                        Uploaded: ${new Date(data.data.uploadedAt).toLocaleString()}<br>
                        <em>${data.data.accessNote}</em>
                    `);
                } else {
                    showResult('error', `Upload failed: ${data.error}<br>Details: ${data.details || 'No additional details'}`);
                }
            } catch (error) {
                showResult('error', `Network error: ${error.message}`);
            } finally {
                uploadBtn.disabled = false;
                progressContainer.style.display = 'none';
            }
        }

        async function getSecureUrl() {
            const token = jwtToken.value.trim();
            const quiz = quizId.value.trim();

            if (!token || !quiz) {
                showResult('error', 'Please enter your JWT token and Quiz ID.');
                return;
            }

            getSecureUrlBtn.disabled = true;

            try {
                const response = await fetch(`/get-quiz-pdf/${quiz}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': token
                    }
                });

                const data = await response.json();

                if (data.success) {
                    showResult('success', `
                        <strong>Secure URL Generated!</strong><br>
                        File: ${data.data.originalName}<br>
                        Size: ${(data.data.fileSize / 1024 / 1024).toFixed(2)} MB<br>
                        Uploaded by: ${data.data.uploadedBy}<br>
                        Upload Date: ${new Date(data.data.uploadDate).toLocaleString()}<br>
                        Expires: ${data.data.expiresIn}<br>
                        <a href="${data.data.secureUrl}" target="_blank">Download PDF (Valid for 15 minutes)</a>
                    `);
                } else {
                    showResult('error', `Failed to get secure URL: ${data.error}<br>Details: ${data.details || 'No additional details'}`);
                }
            } catch (error) {
                showResult('error', `Network error: ${error.message}`);
            } finally {
                getSecureUrlBtn.disabled = false;
            }
        }

        function showResult(type, message) {
            result.className = `result ${type}`;
            result.innerHTML = message;
            result.style.display = 'block';
        }

        function clearAll() {
            fileInput.value = '';
            quizId.value = '';
            fileInfo.style.display = 'none';
            result.style.display = 'none';
            progressContainer.style.display = 'none';
            uploadBtn.disabled = true;
            getSecureUrlBtn.disabled = true;
        }
    </script>
</body>
</html>
