# Google Drive 403 Error Troubleshooting Guide

## Overview
You're getting a 403 error when trying to upload files to Google Drive. This usually indicates an authentication or permission issue.

## Step-by-Step Troubleshooting

### 1. Verify Service Account File Format
Check that your `drive_json.json` file has the correct format:

```json
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

### 2. Enable Google Drive API
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Go to "APIs & Services" > "Library"
4. Search for "Google Drive API"
5. Click on it and press "Enable"

### 3. Check Service Account Permissions
The service account needs proper permissions:

#### Option A: Share a Drive folder with the service account
1. Create a folder in Google Drive
2. Right-click the folder > "Share"
3. Add the service account email (from your JSON file) as an editor
4. Update the code to use this folder as parent

#### Option B: Use domain-wide delegation (if using Google Workspace)
1. In Google Cloud Console, go to "IAM & Admin" > "Service Accounts"
2. Click on your service account
3. Go to "Advanced settings"
4. Enable "Domain-wide delegation"

### 4. Verify API Scopes
Make sure you're using the correct scope. The current code uses:
- `https://www.googleapis.com/auth/drive.file` (can only access files created by the app)

You might need:
- `https://www.googleapis.com/auth/drive` (full Drive access)

### 5. Test with Improved Logging
I've added detailed logging to help debug. Run your upload and check the console output for:
- Service account email and project ID
- JWT payload details
- Exact error response from Google

### 6. Common 403 Error Causes

#### "insufficient_scope" error
- The service account doesn't have the required permissions
- Solution: Use broader scope or share Drive folder with service account

#### "access_denied" error
- API not enabled for the project
- Service account doesn't exist or is disabled
- Solution: Enable Drive API and verify service account

#### "forbidden" error
- Service account key is invalid or expired
- Solution: Generate new service account key

### 7. Quick Test Script
Create a test file to verify your service account works:

```javascript
const fs = require('fs');
const jwt = require('jsonwebtoken');
const axios = require('axios');

async function testServiceAccount() {
  try {
    const serviceAccount = JSON.parse(fs.readFileSync("drive_json.json", "utf8"));
    
    const now = Math.floor(Date.now() / 1000);
    const payload = {
      iss: serviceAccount.client_email,
      scope: "https://www.googleapis.com/auth/drive.file",
      aud: "https://oauth2.googleapis.com/token",
      exp: now + 3600,
      iat: now,
    };
    
    const token = jwt.sign(payload, serviceAccount.private_key, { algorithm: "RS256" });
    
    const response = await axios.post("https://oauth2.googleapis.com/token", {
      grant_type: "urn:ietf:params:oauth:grant-type:jwt-bearer",
      assertion: token,
    });
    
    console.log("✅ Authentication successful!");
    console.log("Access token obtained:", response.data.access_token.substring(0, 20) + "...");
    
    // Test Drive API access
    const driveResponse = await axios.get("https://www.googleapis.com/drive/v3/about?fields=user", {
      headers: {
        Authorization: `Bearer ${response.data.access_token}`,
      },
    });
    
    console.log("✅ Drive API access successful!");
    console.log("Service account user:", driveResponse.data.user);
    
  } catch (error) {
    console.error("❌ Error:", error.response?.data || error.message);
  }
}

testServiceAccount();
```

### 8. Alternative: Use Specific Drive Folder
If you continue having issues, create a specific folder and share it with your service account:

```javascript
// Update the uploadFileToDrive function to use a specific folder
const metadata = {
  name: fileName,
  parents: ["YOUR_SHARED_FOLDER_ID"], // Replace with actual folder ID
};
```

## Next Steps
1. Run the upload again and check the detailed console logs
2. Verify your service account file format
3. Ensure Google Drive API is enabled
4. Try the test script above
5. Share the exact error message from the logs for further assistance

The improved logging will show exactly what's happening during authentication and upload, making it easier to identify the specific issue.
