# Cloudflare R2 Credentials Setup Guide

This guide will help you set up the required credentials for the PDF upload to Cloudflare R2.

## 🔑 Required Credentials

You need the following credentials to use the R2 upload functionality:

1. **R2_ACCOUNT_ID**: Your Cloudflare Account ID
2. **R2_ACCESS_KEY_ID**: R2 Access Key ID
3. **R2_SECRET_ACCESS_KEY**: R2 Secret Access Key
4. **R2_BUCKET_NAME**: Your R2 bucket name

## 📋 Step-by-Step Setup

### Step 1: Get Your Cloudflare Account ID

1. Log in to [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. In the right sidebar, you'll see your **Account ID**
3. Copy this 32-character hexadecimal string

### Step 2: Create R2 API Token

1. In the Cloudflare dashboard, go to **"R2 Object Storage"**
2. Click on **"Manage R2 API tokens"** (usually in the right sidebar)
3. Click **"Create API token"**
4. Configure the token:
   - **Token name**: `NCExams PDF Upload` (or any descriptive name)
   - **Permissions**: Select **"Object Read & Write"**
   - **Account resources**: Choose **"Include - All accounts"** or select your specific account
   - **Bucket resources**: Either:
     - **"Include - All buckets"** for all buckets
     - **"Include - Specific buckets"** and choose your bucket

### Step 3: Get Your Credentials

After creating the token, you'll see:
- **Access Key ID** (this is your `R2_ACCESS_KEY_ID`)
- **Secret Access Key** (this is your `R2_SECRET_ACCESS_KEY`)

⚠️ **Important**: Copy these values immediately as the Secret Access Key will only be shown once!

### Step 4: Create R2 Bucket (if you haven't already)

1. In **R2 Object Storage**, click **"Create bucket"**
2. Choose a unique bucket name (e.g., `ncexams-pdfs`)
3. Select a location close to your users
4. Click **"Create bucket"**

### Step 5: Configure Environment Variables

Create a `.env` file in your project root (if it doesn't exist) and add:

```env
# Cloudflare R2 Configuration
R2_ACCOUNT_ID=your-32-character-account-id
R2_ACCESS_KEY_ID=your-access-key-id-from-step-3
R2_SECRET_ACCESS_KEY=your-secret-access-key-from-step-3
R2_BUCKET_NAME=your-bucket-name-from-step-4
```

### Step 6: Configure Public Access (Optional)

If you want uploaded PDFs to be publicly accessible:

1. Go to your R2 bucket in the dashboard
2. Click on **"Settings"**
3. Under **"Public access"**, click **"Allow Access"**
4. This will give you a public URL format like: `https://pub-{account-id}.r2.dev/`

## 🧪 Testing Your Setup

1. Start your server: `node src/backend.js`
2. Open: `http://localhost:4002/pdf-upload-example.html`
3. Enter your JWT token
4. Try uploading a PDF file

## 🔒 Security Best Practices

1. **Limit Permissions**: Only grant "Object Read & Write" permissions
2. **Bucket-Specific**: Consider creating tokens for specific buckets only
3. **Environment Variables**: Never commit credentials to version control
4. **Token Rotation**: Regularly rotate your API tokens
5. **Monitor Usage**: Check usage in the Cloudflare dashboard

## 🐛 Common Issues

### Issue: "NoSuchBucket" Error
- **Solution**: Check that your bucket name is correct and the bucket exists

### Issue: "InvalidAccessKeyId" Error
- **Solution**: Verify your Access Key ID is correct

### Issue: "SignatureDoesNotMatch" Error
- **Solution**: Check your Secret Access Key is correct

### Issue: "AccessDenied" Error
- **Solution**: Ensure your API token has "Object Read & Write" permissions

## 📝 Example Configuration

Here's an example of what your `.env` file should look like:

```env
# Cloudflare R2 Configuration
R2_ACCOUNT_ID=18aad3dec0d70ebfd4bb8d0f16ced4ec
R2_ACCESS_KEY_ID=a1b2c3d4e5f6g7h8i9j0
R2_SECRET_ACCESS_KEY=k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6
R2_BUCKET_NAME=ncexams-pdfs
```

## 🔄 Next Steps

After setting up your credentials:

1. Test the upload functionality
2. Configure public access if needed
3. Consider setting up a custom domain for your R2 bucket
4. Implement file management features (list, delete, etc.)

## 📞 Support

If you encounter issues:
1. Check the server console for detailed error messages
2. Verify all credentials are correct
3. Ensure your R2 bucket exists and has proper permissions
4. Test with the provided HTML example page

The API will provide detailed error messages to help you troubleshoot any configuration issues.
