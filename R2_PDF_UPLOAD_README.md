# Cloudflare R2 PDF Upload API

This document describes the new PDF upload functionality that allows uploading PDF files directly to a Cloudflare R2 bucket using AWS S3-compatible API.

## 🚀 Features

- **Secure Upload**: JWT authentication required
- **PDF Only**: Validates file type to ensure only PDF files are uploaded
- **File Size Limit**: Maximum 50MB per file
- **Quiz Association**: Links PDF files to specific quiz IDs
- **Database Storage**: Stores file metadata in MySQL database
- **Secure Retrieval**: Temporary signed URLs with 15-minute expiration
- **Access Control**: User permission validation (owner or admin access)
- **Rate Limiting**: Prevents abuse with configurable limits
- **Access Logging**: Comprehensive logging for security monitoring
- **Unique Naming**: Automatic filename generation to prevent conflicts
- **Error Handling**: Comprehensive error handling with detailed messages

## 📋 Prerequisites

1. **Cloudflare R2 Bucket**: You need an active Cloudflare R2 bucket
2. **R2 API Credentials**: Access Key ID and Secret Access Key for R2
3. **Environment Variables**: Configure R2 credentials in your environment
4. **JWT Authentication**: Valid JWT tokens for user authentication

## ⚙️ Setup Instructions

### 1. Install Dependencies

The required AWS SDK dependency should be installed:
```bash
npm install @aws-sdk/client-s3
```

### 2. Configure Environment Variables

Add the following environment variables to your `.env` file:

```env
# Cloudflare R2 Configuration
R2_ACCOUNT_ID=your-cloudflare-account-id
R2_ACCESS_KEY_ID=your-r2-access-key-id
R2_SECRET_ACCESS_KEY=your-r2-secret-access-key
R2_BUCKET_NAME=your-bucket-name
```

### 3. Create R2 Bucket

1. Log in to your Cloudflare dashboard
2. Go to R2 Object Storage
3. Create a new bucket (e.g., `ncexams-pdfs`)
4. Generate R2 API tokens with read/write permissions

### 4. Configure Public Access (Optional)

If you want uploaded files to be publicly accessible:
1. Go to your R2 bucket settings
2. Enable public access
3. Configure custom domain if needed

## 🔧 API Endpoints

### POST `/upload-pdf-to-r2`

Uploads a PDF file to Cloudflare R2 bucket and stores metadata in database.

#### Headers
```
Authorization: Bearer <jwt-token>
Content-Type: multipart/form-data
```

#### Request Body
- `pdf` (file): The PDF file to upload (max 50MB)
- `quizId` (string): The quiz ID to associate with this PDF file

### GET `/get-quiz-pdf/:quizId`

Retrieves a temporary, secure URL for accessing a quiz PDF file.

#### Headers
```
Authorization: Bearer <jwt-token>
```

#### URL Parameters
- `quizId` (string): The quiz ID to retrieve the PDF for

#### Upload Response

**Success (200):**
```json
{
  "success": true,
  "message": "PDF uploaded successfully to Cloudflare R2",
  "data": {
    "fileId": 123,
    "quizId": "quiz_001",
    "fileName": "pdfs/1703123456789_abc123_document.pdf",
    "originalName": "document.pdf",
    "size": 1048576,
    "r2Key": "pdfs/1703123456789_abc123_document.pdf",
    "uploadedAt": "2023-12-21T10:30:45.123Z",
    "uploadedBy": "**********",
    "accessNote": "Use /get-quiz-pdf/quiz_001 to get secure access URL"
  }
}
```

#### Retrieval Response

**Success (200):**
```json
{
  "success": true,
  "message": "Secure access URL generated successfully",
  "data": {
    "fileId": 123,
    "quizId": "quiz_001",
    "fileName": "pdfs/1703123456789_abc123_document.pdf",
    "originalName": "document.pdf",
    "fileSize": 1048576,
    "uploadedBy": "**********",
    "uploadDate": "2023-12-21T10:30:45.000Z",
    "secureUrl": "https://your-account-id.r2.cloudflarestorage.com/pdfs/document.pdf?X-Amz-Algorithm=...",
    "expiresIn": "15 minutes",
    "expiresAt": "2023-12-21T10:45:45.123Z"
  }
}
```

**Error (400/500):**
```json
{
  "success": false,
  "error": "Error description",
  "details": "Additional error details"
}
```

## 📝 Usage Examples

### JavaScript/Fetch API

```javascript
const uploadPDF = async (file, jwtToken) => {
  const formData = new FormData();
  formData.append('pdf', file);

  try {
    const response = await fetch('/upload-pdf-to-r2', {
      method: 'POST',
      headers: {
        'Authorization': jwtToken
      },
      body: formData
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('Upload successful:', result.data.url);
    } else {
      console.error('Upload failed:', result.error);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
};
```

### cURL

**Upload PDF:**
```bash
curl -X POST \
  http://localhost:4002/upload-pdf-to-r2 \
  -H "Authorization: Bearer your-jwt-token" \
  -F "pdf=@/path/to/your/document.pdf" \
  -F "quizId=quiz_001"
```

**Get Secure URL:**
```bash
curl -X GET \
  http://localhost:4002/get-quiz-pdf/quiz_001 \
  -H "Authorization: Bearer your-jwt-token"
```

### React Component Example

```jsx
import React, { useState } from 'react';

const PDFUploader = () => {
  const [file, setFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [result, setResult] = useState(null);

  const handleUpload = async () => {
    if (!file) return;

    setUploading(true);
    const formData = new FormData();
    formData.append('pdf', file);

    try {
      const response = await fetch('/upload-pdf-to-r2', {
        method: 'POST',
        headers: {
          'Authorization': localStorage.getItem('jwt_token')
        },
        body: formData
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ success: false, error: error.message });
    } finally {
      setUploading(false);
    }
  };

  return (
    <div>
      <input 
        type="file" 
        accept=".pdf"
        onChange={(e) => setFile(e.target.files[0])}
      />
      <button onClick={handleUpload} disabled={!file || uploading}>
        {uploading ? 'Uploading...' : 'Upload PDF'}
      </button>
      {result && (
        <div>
          {result.success ? (
            <p>Success: <a href={result.data.url}>{result.data.fileName}</a></p>
          ) : (
            <p>Error: {result.error}</p>
          )}
        </div>
      )}
    </div>
  );
};
```

## 🗄️ Database Schema

The system automatically creates a `quiz_files` table with the following structure:

```sql
CREATE TABLE quiz_files (
  id INT AUTO_INCREMENT PRIMARY KEY,
  quiz_id VARCHAR(255) NOT NULL,
  file_name VARCHAR(500) NOT NULL,
  original_name VARCHAR(500) NOT NULL,
  r2_key VARCHAR(500) NOT NULL,
  file_size BIGINT NOT NULL,
  uploaded_by VARCHAR(50) NOT NULL,
  upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  file_url TEXT,
  INDEX idx_quiz_id (quiz_id),
  INDEX idx_uploaded_by (uploaded_by),
  INDEX idx_upload_date (upload_date)
);
```

## 🔒 Security Features

- **JWT Authentication**: All uploads and retrievals require valid JWT token
- **Temporary URLs**: Generated URLs expire after 15 minutes
- **Access Control**: Only file owners or admins can access files
- **Rate Limiting**: Prevents abuse (5 uploads/15min, 20 retrievals/15min)
- **File Type Validation**: Only PDF files are accepted
- **File Size Limits**: Maximum 50MB per file
- **Unique Filenames**: Prevents file conflicts and overwrites
- **Access Logging**: All access attempts are logged for security monitoring
- **User Tracking**: Records who uploaded each file and when
- **Permission Validation**: Ensures users can only access authorized files

## 🐛 Error Handling

The API handles various error scenarios:

- **No file uploaded**: Returns 400 error
- **Invalid file type**: Returns 400 error for non-PDF files
- **File too large**: Multer automatically rejects files > 50MB
- **Missing JWT token**: Returns 401 authentication error
- **R2 bucket not found**: Returns 500 with specific error message
- **Invalid R2 credentials**: Returns 500 with authentication error
- **Network/upload errors**: Returns 500 with error details

## 📁 File Organization

Uploaded files are organized in the R2 bucket as:
```
bucket-name/
└── pdfs/
    ├── 1703123456789_abc123_document1.pdf
    ├── 1703123456790_def456_document2.pdf
    └── ...
```

Filename format: `pdfs/{timestamp}_{random}_{sanitized-original-name}.pdf`

## 🧪 Testing

Use the provided example HTML file at `/public/pdf-upload-example.html` to test the upload functionality:

1. Open `http://localhost:4002/pdf-upload-example.html` in your browser
2. Enter your JWT token
3. Select or drag-drop a PDF file
4. Click "Upload PDF"

## 🔧 Configuration Options

You can modify the following settings in `backend.js`:

- **File size limit**: Change `fileSize` in multer configuration
- **Bucket name**: Update `R2_BUCKET_NAME` environment variable
- **File path**: Modify the `fileName` generation logic
- **Metadata**: Add additional metadata in upload parameters

## 📞 Support

If you encounter any issues:

1. Check your R2 credentials and bucket configuration
2. Verify the bucket exists and has proper permissions
3. Ensure your JWT token is valid
4. Check the server logs for detailed error messages

## 🔄 Next Steps

Consider implementing:
- File deletion endpoint
- File listing endpoint
- Progress tracking for large uploads
- Thumbnail generation for PDF previews
- File sharing and access controls
